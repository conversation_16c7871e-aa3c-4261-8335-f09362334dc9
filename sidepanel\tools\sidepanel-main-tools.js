// sidepanel-main-tools.js

console.log("sidepanel-main-tools.js loading");

// Reference the tools tab content element
// Note: We don't redeclare it here since it's already declared in sidepanel-ui-tools.js

// Import click-utils.js if it's not already loaded
if (typeof window.clickUtils === 'undefined') {
    console.log("Main-Tools: Loading click-utils.js");
    const script = document.createElement('script');
    script.src = '/sidepanel/common/click-utils.js';
    document.head.appendChild(script);
}

// Function to set up event listeners for the Tools tab
function setupToolsEventListeners() {
    console.log("Main-Tools: Setting up tools event listeners...");

    // Add click event listener for the tools tab content
    const toolsTab = document.getElementById('tools-tab');
    toolsTab?.addEventListener('click', (event) => {
        const target = event.target;
        const button = target.closest('button');

        if (!button) return;

        // Handle click button
        if (button.matches('.click-button')) {
            const inputField = button.closest('.field-group-with-button')?.querySelector('input[type="text"]');
            if (inputField) {
                // Call the global handleSelectorClick function defined in sidepanel-ui-tools.js
                window.handleSelectorClick(inputField.value);
            } else {
                console.error("Main-Tools: Could not find input field for click button.");
                alert("Error: Could not link button to input field.");
            }
        }
        // Handle convert button for XPath to CSS
        else if (button.matches('.convert-button')) {
            const inputField = button.closest('.field-group-with-button')?.querySelector('input[type="text"]');
            if (inputField) {
                // Call the global handleXPathToCssConversion function defined in sidepanel-ui-tools.js
                window.handleXPathToCssConversion(inputField.value);
            } else {
                console.error("Main-Tools: Could not find input field for convert button.");
                alert("Error: Could not link button to input field.");
            }
        }
    });

    console.log("Main-Tools: Tools event listeners setup complete.");
}

// Make the function available globally
window.setupToolsEventListeners = setupToolsEventListeners;

console.log("sidepanel-main-tools.js loaded");
