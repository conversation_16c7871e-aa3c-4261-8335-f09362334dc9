// sidepanel-logic-admin.js


async function loadSiteJSONDataAndUpdateUI(url) {
    if (!url) {
        // Instead of warning, check if we have a current URL in state
        if (state.currentUrl) {
            url = state.currentUrl;
        } else {
            clearSiteDataState(); // Clear site ID and form data (logic-common)
            updateAdminFormFields([]); // (ui-admin)
            updateSubmitFormFields([]); // (ui-submit)
            clearReportSiteFields(); // Clear related fields (ui-submit)
            updateUnifiedStatus(); // Update header/footer (ui-common)
            return;
        }
    }
    // Maybe show loading indicator?

    try {
        const result = await getSiteJSON(url); // (api-service)

        if (result.status === 'success' && result.json_data) {
            state.currentSiteId = result.site_id || null; // Update common state
            state.currentSiteUrl = result.site_url || null; // Update common state
            state.siteFormData = Array.isArray(result.json_data) ? result.json_data : []; // Update common state
            updateAdminFormFields(state.siteFormData); // Update Admin UI (ui-admin)
            updateSubmitFormFields(state.siteFormData); // Update Submit UI (ui-submit)
            // Load and restore the "Ready" checkbox state
            await loadConfigurationReadyState();
            // Fetch related report site data now that we have site ID
            await fetchReportSiteDataAndUpdateUI(); // (logic-submit -> ui-submit)
        } else if (result.status === 'not-found') {
             clearSiteDataState(); // Clear site ID and form data (logic-common)
             state.siteFormData = []; // Ensure state is empty array
             updateAdminFormFields([]); // Update Admin UI (ui-admin)
             updateSubmitFormFields([]); // Update Submit UI (ui-submit)
             clearReportSiteFields(); // Clear related fields (ui-submit)
             // Clear the "Ready" checkbox
             clearConfigurationReadyState();
        } else {
            // Handle other errors from getSiteJSON
            console.error('Logic-Admin: Error loading site JSON:', result.message);
            clearSiteDataState(); // Clear site state (logic-common)
            updateAdminFormFields([]); // Clear UI (ui-admin)
            updateSubmitFormFields([]); // Clear UI (ui-submit)
            clearReportSiteFields(); // Clear related fields (ui-submit)
            // Maybe show an error message in the UI
        }
    } catch (error) {
        // Handle fetch/network errors
        console.error('Logic-Admin: Exception loading site JSON:', error);
        clearSiteDataState(); // Clear site state (logic-common)
        updateAdminFormFields([]); // Clear UI (ui-admin)
        updateSubmitFormFields([]); // Clear UI (ui-submit)
        clearReportSiteFields(); // Clear related fields (ui-submit)
        // Clear the "Ready" checkbox
        clearConfigurationReadyState();
        // Maybe show an error message in the UI
    } finally {
        updateUnifiedStatus(); // Update header/footer (ui-common)
        // Hide loading indicator?
    }
}


function generateAdminFieldsJSON() {
    const fields = [];
    // Ensure adminFieldsContentUI is defined and accessible
    const adminFieldsContent = document.getElementById('admin-form-fields-content');

    // Get the status checkboxes state
    const configurationReadyCheckbox = document.getElementById('configurationReady');
    const configurationReviewedCheckbox = document.getElementById('configurationReviewed');
    const isConfigurationReady = configurationReadyCheckbox?.checked || false;
    const isConfigurationReviewed = configurationReviewedCheckbox?.checked || false;

    // Get all page accordions
    const pageAccordions = adminFieldsContent?.querySelectorAll('.page-accordion');

    if (pageAccordions && pageAccordions.length > 0) {
        // Process fields within each page accordion
        pageAccordions.forEach(pageAccordion => {
            const pageId = pageAccordion.getAttribute('data-page-id');
            const pageTitle = pageAccordion.querySelector('.page-title')?.textContent || '';

            // Get the fields container for this page
            const fieldsContainer = pageAccordion.querySelector('.page-fields-container');

            if (fieldsContainer) {
                // Process each field in this page
                fieldsContainer.querySelectorAll('.dynamic-field-container').forEach(container => {
                    // Helper to get value safely from a class selector within the container
                    const getValByClass = (className) => container.querySelector(`.${className}`)?.value ?? '';
                    const getBoolByClass = (className) => container.querySelector(`.${className}`)?.value === 'true';

                    const fieldData = {
                        type: getValByClass('field-type'),
                        label: getValByClass('field-label'),
                        match: getValByClass('field-match'),
                        required: getBoolByClass('field-required'),
                        selector: getValByClass('field-selector'),
                        optionSelector: getValByClass('field-option-selector'),
                        maxlength: getValByClass('field-maxlength'),
                        value: getValByClass('field-value'),
                        defaultOption: getValByClass('field-default-option'),
                        delay: getValByClass('field-delay'),
                        pageId: pageId,
                        pageName: pageTitle,
                        options: [] // Initialize options array
                    };

                    // Find options specifically within this field container
                    container.querySelectorAll('.option-item').forEach(optItem => {
                        // Similarly, use class names instead of constructed IDs for option elements
                        const getOptValByClass = (className) => optItem.querySelector(`.${className}`)?.value ?? '';

                        fieldData.options.push({
                            label: getOptValByClass('option-label'),
                            selector: getOptValByClass('option-selector'),
                        });
                    });

                    // Remove options array if it's empty, unless API requires it
                    if (fieldData.options.length === 0) {
                        delete fieldData.options;
                    }

                    fields.push(fieldData);
                });
            }
        });
    } else {
        // Fallback to the old method if no page accordions are found
        adminFieldsContent?.querySelectorAll('.dynamic-field-container').forEach(container => {
            // Helper to get value safely from a class selector within the container
            const getValByClass = (className) => container.querySelector(`.${className}`)?.value ?? '';
            const getBoolByClass = (className) => container.querySelector(`.${className}`)?.value === 'true';

            const fieldData = {
                type: getValByClass('field-type'),
                label: getValByClass('field-label'),
                match: getValByClass('field-match'),
                required: getBoolByClass('field-required'),
                selector: getValByClass('field-selector'),
                optionSelector: getValByClass('field-option-selector'),
                maxlength: getValByClass('field-maxlength'),
                value: getValByClass('field-value'),
                defaultOption: getValByClass('field-default-option'),
                delay: getValByClass('field-delay'),
                options: [] // Initialize options array
            };

            // Find options specifically within this field container
            container.querySelectorAll('.option-item').forEach(optItem => {
                // Similarly, use class names instead of constructed IDs for option elements
                const getOptValByClass = (className) => optItem.querySelector(`.${className}`)?.value ?? '';

                fieldData.options.push({
                    label: getOptValByClass('option-label'),
                    selector: getOptValByClass('option-selector'),
                });
            });

            // Remove options array if it's empty, unless API requires it
            if (fieldData.options.length === 0) {
                delete fieldData.options;
            }

            fields.push(fieldData);
        });
    }

    // Return an object that includes both fields and configuration state
    return {
        fields: fields,
        configurationReady: isConfigurationReady,
        configurationReviewed: isConfigurationReviewed
    };
}

// Flag to prevent multiple simultaneous element processing
let isProcessingElement = false;

async function processSelectedElement(elementInfo) {
    // Check if we're already processing an element
    if (isProcessingElement) {
        return;
    }

    // Double-check that we're not in selector generation mode
    // This is a critical safeguard to prevent the AI API from being called when we're just generating a selector
    if (state.isGeneratingSelectorFor) {
        // Reset the state as an additional safety measure
        state.isGeneratingSelectorFor = null;
        resetSelectButtons();
        return; // Stop execution here
    }

    // Check the global selector generation mode flag
    // This is an additional safeguard to prevent AI processing when using the selector field button
    if (window.isSelectorGenerationMode === true) {
        // Reset the flag as an additional safety measure
        window.isSelectorGenerationMode = false;
        resetSelectButtons();
        return; // Stop execution here
    }

    // Set the processing flag to prevent duplicate processing
    isProcessingElement = true;
    if (!elementInfo || !state.currentTabId) { // Use state.currentTabId
        alert('Cannot process selection: Missing element info or tab context.');
        resetSelectButtons(); // Ensure buttons reset if we abort early (ui-common)
        isProcessingElement = false; // Reset the processing flag
        return;
    }

    // Show processing indicator if needed (e.g., modify a status area)
    // showProcessingIndicator(true); // Example UI function call

    try {
        let fullHtml = elementInfo.outerHTML; // Prefer outerHTML as a base
        // Attempt to get full HTML if outerHTML seems insufficient or a path exists
        if (!fullHtml && elementInfo.absolutePath) {
             try {
                 // Use the proxy mechanism
                 const response = await chrome.runtime.sendMessage({
                     action: 'proxyRequestToContentScript',
                     targetTabId: state.currentTabId, // Use state
                     contentScriptAction: 'getFullHTML',
                     payload: { selector: elementInfo.absolutePath }
                 });
                 if (response?.success && response.html) {
                     fullHtml = response.html;
                 } else {
                     // Use outerHTML if available, otherwise fallback to innerHTML wrapper
                     if (!fullHtml) fullHtml = `<div>${elementInfo.innerHTML || ''}</div>`;
                 }
             } catch (fetchError) {
                  if (!fullHtml) fullHtml = `<div>${elementInfo.innerHTML || ''}</div>`; // Fallback
             }
        } else if (!fullHtml) {
             // Fallback if outerHTML was also missing (unlikely but possible)
             fullHtml = `<div>${elementInfo.innerHTML || ''}</div>`;
        }

        // Minimize HTML using the function from sidepanel-html-utils.js
        const minimizedHTML = minimizeHtmlRegex(fullHtml);
        // Call AI service using the function from sidepanel-ai-service.js
        const generatedFields = await callAIForFormJSON(minimizedHTML);

        if (generatedFields?.length > 0) {
            // Process each field to clear Match Report Field for excluded types
            const processedFields = generatedFields.map(field => {
                // List of field types that should not have a Match Report Field
                const excludedTypes = ['button']; // Removed radio and checkbox groups to allow matching

                // If the field type is in the excluded list, clear the match property
                if (excludedTypes.includes(field.type)) {
                    field.match = '';
                }

                return field;
            });

            // Add fields to UI (ui-admin)
            addGeneratedFieldsToAdminUI(processedFields);
            // Provide feedback
            alert(`Added ${processedFields.length} fields based on selection.`); // This is the alert being shown
            // Optionally switch tab and scroll
             switchToAdminTab(); // (ui-common)
             scrollAdminFieldsToBottom(); // (ui-common)
        } else {
            // No fields generated or error in OpenAI call (handled internally by callOpenAI...)
            // Alert might have already been shown by callOpenAIForFormJSON on error.
            // Add a specific alert if the array is just empty.
            if (generatedFields && generatedFields.length === 0) {
                alert('No form fields identified in the selected element.');
            }
        }

    } catch (error) {
        console.error("Error processing selection:", error);
        alert(`Processing Error: ${error.message}`);
    } finally {
        // Reset the processing flag
        isProcessingElement = false;
        // Button reset is now handled by the 'selectionComplete' message in logic-common -> ui-common
        // showProcessingIndicator(false); // Hide processing indicator
    }
}

/**
 * Loads both "Configuration Ready" and "Configuration Reviewed" checkbox states from local storage
 */
async function loadConfigurationReadyState() {
    if (!state.currentSiteId) {
        return;
    }

    try {
        const readyStateKey = `configReady_${state.currentSiteId}`;
        const reviewedStateKey = `configReviewed_${state.currentSiteId}`;
        const result = await new Promise(resolve => {
            chrome.storage.local.get([readyStateKey, reviewedStateKey], resolve);
        });

        const isReady = result[readyStateKey] || false;
        const isReviewed = result[reviewedStateKey] || false;

        const configurationReadyCheckbox = document.getElementById('configurationReady');
        const configurationReviewedCheckbox = document.getElementById('configurationReviewed');

        if (configurationReadyCheckbox) {
            configurationReadyCheckbox.checked = isReady;
        }
        if (configurationReviewedCheckbox) {
            configurationReviewedCheckbox.checked = isReviewed;
        }
    } catch (error) {
        console.error('Error loading configuration status states:', error);
    }
}

/**
 * Clears both "Configuration Ready" and "Configuration Reviewed" checkbox states
 */
function clearConfigurationReadyState() {
    const configurationReadyCheckbox = document.getElementById('configurationReady');
    const configurationReviewedCheckbox = document.getElementById('configurationReviewed');

    if (configurationReadyCheckbox) {
        configurationReadyCheckbox.checked = false;
    }
    if (configurationReviewedCheckbox) {
        configurationReviewedCheckbox.checked = false;
    }
}


