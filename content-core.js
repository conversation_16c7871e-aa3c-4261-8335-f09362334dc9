// content-core.js - Core initialization and utility functions

if (typeof window.enableSelectionMode !== 'function') {
    console.warn('common/element-utils.js not found or loaded yet.');
}

// Note: isElementInViewport is now defined in content-highlight.js
// We'll check if it's already defined before redefining it
if (typeof window.isElementInViewport !== 'function') {
    // Helper function to check if an element is in the viewport
    function isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Make the function available globally
    window.isElementInViewport = isElementInViewport;
}

// Track all selectors from the sidepanel for hover detection
window.knownSelectors = window.knownSelectors || new Set();

// Initialize the extension when the document is fully loaded
document.addEventListener('DOMContentLoaded', () => {

    try {
        // Initialize page element hover listeners (defined in content-hover.js)
        if (typeof window.setupPageElementHoverListeners === 'function') {
            window.setupPageElementHoverListeners();
        } else {
            console.warn('setupPageElementHoverListeners function not available yet');
            // Try again after a short delay
            setTimeout(() => {
                if (typeof window.setupPageElementHoverListeners === 'function') {
                    window.setupPageElementHoverListeners();
                } else {
                    console.error('setupPageElementHoverListeners function not available');
                }
            }, 500);
        }
    } catch (e) {
        console.error("Form Analyzer: Failed to initialize:", e);
    }
});


