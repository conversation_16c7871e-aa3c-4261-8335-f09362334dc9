// content-actions.js - Main dispatcher for element manipulation functions
// This file coordinates all element action handlers from separate modules

// Handler for all element action messages
function handleElementAction(message, sendResponse) {
    const action = message.action;

    console.log(`Content-Actions: Received action: ${action}`, message);

    switch (action) {
        // Basic element operations (handled in content-actions-basic.js)
        case 'setElementValue':
            if (typeof window.handleSetElementValue === 'function') {
                window.handleSetElementValue(message, sendResponse);
            } else {
                console.error('handleSetElementValue not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;
        case 'clickElement':
            if (typeof window.handleClickElement === 'function') {
                window.handleClickElement(message, sendResponse);
            } else {
                console.error('handleClickElement not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;
        case 'setCheckboxState':
        case 'setRadioState':
            if (typeof window.handleSetCheckboxRadioState === 'function') {
                window.handleSetCheckboxRadioState(message, sendResponse);
            } else {
                console.error('handleSetCheckboxRadioState not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;

        // Select element operations (handled in content-actions-select.js)
        case 'getSelectOptions':
            if (typeof window.handleGetSelectOptions === 'function') {
                window.handleGetSelectOptions(message, sendResponse);
            } else {
                console.error('handleGetSelectOptions not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;
        case 'selectOption':
            if (typeof window.handleSelectOption === 'function') {
                window.handleSelectOption(message, sendResponse);
            } else {
                console.error('handleSelectOption not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;

        // File operations (handled in content-actions-file.js)
        case 'uploadFileFromUrl':
            if (typeof window.handleUploadFileFromUrl === 'function') {
                window.handleUploadFileFromUrl(message, sendResponse);
            } else {
                console.error('handleUploadFileFromUrl not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;
        case 'simulateDragDropFile':
            if (typeof window.handleSimulateDragDropFile === 'function') {
                window.handleSimulateDragDropFile(message, sendResponse);
            } else {
                console.error('handleSimulateDragDropFile not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;

        // HTML retrieval (handled in content-actions-utils.js)
        case 'getFullHTML':
            if (typeof window.handleGetFullHTML === 'function') {
                window.handleGetFullHTML(message, sendResponse);
            } else {
                console.error('handleGetFullHTML not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;

        // Iframe operations (handled in content-actions-iframe.js)
        case 'setIframeValue':
            if (typeof window.handleSetIframeValue === 'function') {
                window.handleSetIframeValue(message, sendResponse);
            } else {
                console.error('handleSetIframeValue not available');
                sendResponse({ success: false, error: 'Handler not available' });
            }
            break;


        default:
            console.warn(`Unknown element action: ${action}`);
            sendResponse({ success: false, error: `Unknown action: ${action}` });
    }
}

// Make the main handler available globally
window.handleElementAction = handleElementAction;

console.log('Content-Actions: Main dispatcher loaded');
