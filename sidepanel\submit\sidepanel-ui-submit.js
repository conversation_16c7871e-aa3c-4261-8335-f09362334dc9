// sidepanel-ui-submit.js

console.log("sidepanel-ui-submit.js loading");

const submitFieldsContentUI = document.getElementById('submit-form-fields-content');

// Function to validate selectors and update UI in the submit tab
async function validateSubmitSelectors() {
    if (!state.currentTabId) return;

    const selectorInputs = submitFieldsContentUI.querySelectorAll('.selector-input');
    console.log(`UI-Submit: Processing ${selectorInputs.length} selectors (validation skipped)`);

    for (const selectorInput of selectorInputs) {
        // Remove all validation classes from the input
        selectorInput.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        // Remove all validation classes from the container
        const container = selectorInput.closest('.dynamic-field-container');
        if (container) {
            container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
        }

        // Skip validation entirely - no need to check selectors
    }

    // Update content script with selectors after processing is complete
    updateContentScriptWithSelectors();
}

function updateSubmitFormFields(formDefinition) {
    submitFieldsContentUI.innerHTML = '';
    if (!formDefinition || formDefinition.length === 0) {
        submitFieldsContentUI.innerHTML = '<p class="loading-placeholder">No form configuration found for this site.</p>'; // Use class
        return;
    }

    const fieldsToDisplay = formDefinition.filter(item => item.type !== 'form-container' && item.type !== 'button-submit');

    if (fieldsToDisplay.length === 0) {
        submitFieldsContentUI.innerHTML = '<p class="loading-placeholder">No submittable fields configured.</p>'; // Use class
        return;
    }

    console.log("UI-Submit: DEBUG - Report field values for populating fields:", state.reportFieldValues);

    // Group fields by page
    const fieldsByPage = {};

    // Check if fields have page information
    const hasPageInfo = fieldsToDisplay.some(field => field.pageId);

    if (hasPageInfo) {
        // Group fields by page
        fieldsToDisplay.forEach((field) => {
            const pageId = field.pageId || 'default';
            if (!fieldsByPage[pageId]) {
                fieldsByPage[pageId] = [];
            }
            fieldsByPage[pageId].push(field);
        });

        // Create page accordions for each page
        Object.keys(fieldsByPage).forEach((pageId, pageIndex) => {
            const pageFields = fieldsByPage[pageId];
            const pageTitle = pageFields[0].pageName || (pageId === 'default' ? 'Page 1' : `Page ${pageIndex + 1}`);

            // Create page accordion
            const pageElement = document.createElement('div');
            pageElement.className = 'page-accordion';
            pageElement.setAttribute('data-page-id', pageId);

            // Create header with title and match button
            const headerElement = document.createElement('div');
            headerElement.className = 'page-accordion-header';

            const titleSpan = document.createElement('span');
            titleSpan.className = 'page-title';
            titleSpan.textContent = pageTitle;

            const matchButton = document.createElement('button');
            matchButton.className = 'match-page-button';
            matchButton.textContent = 'Match & Fill';
            matchButton.setAttribute('data-page-id', pageId);

            headerElement.appendChild(titleSpan);
            headerElement.appendChild(matchButton);

            // Create content container for fields
            const contentElement = document.createElement('div');
            contentElement.className = 'page-accordion-content';
            contentElement.style.display = 'none'; // Initially hidden

            // Add fields to the page
            pageFields.forEach((field, fieldIndex) => {
                // Get the value from report field values if available
                let fieldValue = '';
                if (field.match && state.reportFieldValues && state.reportFieldValues[field.match] !== undefined) {
                    fieldValue = state.reportFieldValues[field.match];
                } else {
                    console.log(`UI-Submit: DEBUG - No value found for field ${field.match || 'unnamed'} in report fields`);
                }

                const fieldWithValue = {
                    ...field,
                    value: fieldValue
                };

                const fieldElement = createSubmitFieldElement(fieldIndex, fieldWithValue);
                contentElement.appendChild(fieldElement);
            });

            // Add toggle functionality to header
            headerElement.addEventListener('click', (event) => {
                // Don't toggle if clicking on the match button
                if (event.target.tagName === 'BUTTON') {
                    return;
                }

                contentElement.style.display = contentElement.style.display === 'none' ? 'block' : 'none';
            });

            // Assemble the page accordion
            pageElement.appendChild(headerElement);
            pageElement.appendChild(contentElement);

            // Add to the submit fields content
            submitFieldsContentUI.appendChild(pageElement);

            // Initialize accordion for this page's fields
            initializeSubmitAccordion(contentElement);
        });
    } else {
        // No page information, use the old method
        fieldsToDisplay.forEach((field, index) => {
            // Get the value from report field values if available
            let fieldValue = '';
            if (field.match && state.reportFieldValues && state.reportFieldValues[field.match] !== undefined) {
                fieldValue = state.reportFieldValues[field.match];
                console.log(`UI-Submit: DEBUG - Field ${field.match} has value "${fieldValue}" from report fields`);
            } else {
                console.log(`UI-Submit: DEBUG - No value found for field ${field.match || 'unnamed'} in report fields`);
            }

            const fieldWithValue = {
                ...field,
                value: fieldValue
            };

            const fieldElement = createSubmitFieldElement(index, fieldWithValue);
            submitFieldsContentUI.appendChild(fieldElement);
        });

        // Initialize accordion for all fields
        initializeSubmitAccordion(submitFieldsContentUI);
    }

    // Update content script with selectors after form fields are updated
    setTimeout(() => {
        updateContentScriptWithSelectors();
    }, 500);
}

function populateReportSiteFields(data) {
    if (!data) return;
    try {
        const checkedInput = document.getElementById('checked');
        const formScreenshotInput = document.getElementById('form_screenshot_url');
        const confirmationScreenshotInput = document.getElementById('confirmation_screenshot_url');

        if (checkedInput) checkedInput.checked = data.checked || false;
        if (formScreenshotInput) formScreenshotInput.value = data.form_screenshot_url || '';
        if (confirmationScreenshotInput) confirmationScreenshotInput.value = data.confirmation_screenshot_url || '';

        console.log("UI-Submit: Populated report site fields.");
    } catch (e) {
        console.error("UI-Submit: Error populating report site fields:", e);
    }
}

function clearReportSiteFields() {
     try {
        const checkedInput = document.getElementById('checked');
        const formScreenshotInput = document.getElementById('form_screenshot_url');
        const confirmationScreenshotInput = document.getElementById('confirmation_screenshot_url');

        if (checkedInput) checkedInput.checked = false;
        if (formScreenshotInput) formScreenshotInput.value = '';
        if (confirmationScreenshotInput) confirmationScreenshotInput.value = '';

        console.log("UI-Submit: Cleared report site fields.");
     } catch (e) {
          console.warn("UI-Submit: Error clearing report site fields:", e);
     }
}


function initializeSubmitAccordion(container = null) {
    // If no container is provided, use the default submitFieldsContentUI
    const targetContainer = container || submitFieldsContentUI;
    const $targetContainer = $(targetContainer);

    try { if ($targetContainer.hasClass('ui-accordion')) $targetContainer.accordion('destroy'); } catch (e) { console.warn("Error destroying submit fields accordion:", e); }

    $targetContainer.accordion({
        header: "> div.dynamic-field-container > h3.ui-accordion-header",
        collapsible: true,
        heightStyle: "content",
        active: false,
        icons: false,
        // When an accordion is activated (opened), update the content script with selectors
        activate: function(_, ui) {
            if (ui.newPanel && ui.newPanel.length) {
                // Update selectors after a short delay to ensure the panel is fully open
                setTimeout(() => {
                    updateContentScriptWithSelectors();
                }, 100);
            }
        }
    });

    // Remove all validation classes from selectors
    const selectorInputs = targetContainer.querySelectorAll('.selector-input');
    selectorInputs.forEach(input => {
        // Remove all validation classes from the input
        input.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        // Remove all validation classes from the container
        const container = input.closest('.dynamic-field-container');
        if (container) {
            container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
        }
    });

    // Validate selectors after a short delay to ensure the page is loaded
    setTimeout(() => {
        validateSubmitSelectors();
    }, 500);
}

// Function to handle matching fields for a specific page
function handleMatchPage(pageId) {
    console.log(`Matching fields for page: ${pageId}`);

    // Find the page accordion to get the content element
    const pageAccordion = document.querySelector(`.page-accordion[data-page-id="${pageId}"]`);
    if (!pageAccordion) {
        console.error(`Page accordion not found for page ID: ${pageId}`);
        return;
    }

    // Find the content element to pass to matchFieldsInPage
    const pageContent = pageAccordion.querySelector('.page-accordion-content');
    if (!pageContent) {
        console.error(`Page content not found for page ID: ${pageId}`);
        return;
    }

    // Check if we have siteFormData
    if (!state.siteFormData || state.siteFormData.length === 0) {
        console.error('No siteFormData available');
        alert('No form configuration found. Please configure the form first.');
        return;
    }

    console.log('FIELDS JSON:', state.siteFormData);
    console.log('reportFieldValues:', JSON.stringify(state.reportFieldValues));

    // Filter siteFormData to get only fields for this page
    const pageFields = state.siteFormData.filter(field => {
        // Match by pageId (could be 'default' for the first page)
        return field.pageId === pageId ||
               (pageId === 'default' && (!field.pageId || field.pageId === ''));
    });

    console.log(`Found ${pageFields.length} fields for page ${pageId}:`, pageFields);

    // Check if we have any fields for this page
    if (pageFields.length === 0) {
        console.error(`No fields found in siteFormData for page ID: ${pageId}`);
        alert(`No fields found for this page. Please configure the form first.`);
        return;
    }

    // Filter fields that either have selectors OR are radio/checkbox groups with defaultOption
    const fieldsToProcess = pageFields.filter(field => {
        // Include fields with selectors
        if (field.selector && field.selector.trim() !== '') {
            return true;
        }

        // Include radio/checkbox groups that have a defaultOption
        if ((field.type === 'radio-group' || field.type === 'checkbox-group' ||
             field.type === 'custom-radio-group' || field.type === 'custom-checkbox-group') &&
            field.defaultOption && field.defaultOption.trim() !== '') {
            return true;
        }

        return false;
    });

    if (fieldsToProcess.length === 0) {
        console.error('No processable fields found in siteFormData for this page');
        alert('No fields with selectors or defaultOptions found in this page. Please configure selectors or defaultOptions first.');
        return;
    }

    console.log(`Found ${fieldsToProcess.length} processable fields for page ${pageId}`);

    // Update the button to show processing state
    const matchButton = pageAccordion.querySelector('.match-page-button');
    if (matchButton) {
        matchButton.disabled = true;
        matchButton.textContent = 'Processing...';
    }

    // Call matchFieldsInPage with the filtered fields and handle the result
    matchFieldsInPage(fieldsToProcess)
        .then(result => {
            console.log(`Processed ${result.fieldsProcessed} fields with ${result.successCount} successes and ${result.errorCount} errors`);

            // Update the button text to show the result
            if (matchButton) {
                if (result.errorCount > 0) {
                    matchButton.textContent = `Completed (${result.successCount}/${result.fieldsProcessed})`;
                } else {
                    matchButton.textContent = 'Completed ✓';
                }

                // Reset the button after 3 seconds
                setTimeout(() => {
                    matchButton.disabled = false;
                    matchButton.textContent = 'Match & Fill';
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error in matchFieldsInPage:', error);

            // Update the button to show error
            if (matchButton) {
                matchButton.textContent = 'Error!';

                // Reset the button after 3 seconds
                setTimeout(() => {
                    matchButton.disabled = false;
                    matchButton.textContent = 'Match & Fill';
                }, 3000);
            }
        });
}


function createSubmitFieldElement(fieldIndex, fieldData = {}) {
    const fieldId = `submit-field-${fieldIndex}`;
    const fieldElement = document.createElement('div');
    fieldElement.className = 'dynamic-field-container';
    fieldElement.setAttribute('data-field-index', fieldIndex);

    const headerElement = document.createElement('h3');
    headerElement.className = 'field-header ui-accordion-header ui-state-default ui-corner-top';
    headerElement.textContent = fieldData.label ? escapeHtml(fieldData.label) : `Field ${fieldIndex + 1}`;
    fieldElement.appendChild(headerElement);

    const panelElement = document.createElement('div');
    panelElement.className = 'field-panel ui-accordion-content ui-widget-content ui-corner-bottom';

    const propertiesTable = document.createElement('table');
    propertiesTable.className = 'properties-table';
    const tbody = document.createElement('tbody');

    // Standard properties to show (excluding 'value' as it's shown in the textarea below)
    const propertiesToShow = ['type', 'match', 'selector', 'required', 'maxlength'];
    propertiesToShow.forEach(key => {
        if (fieldData.hasOwnProperty(key) && fieldData[key] !== null && fieldData[key] !== undefined && fieldData[key] !== '') {
            const row = tbody.insertRow();
            const th = document.createElement('th');
            const td = document.createElement('td');
            th.textContent = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
            let displayValue = fieldData[key];
            if (typeof displayValue === 'boolean') displayValue = displayValue ? 'Yes' : 'No';
            if (key === 'selector') {
                // No longer adding validation classes
                td.innerHTML = `<input type="text" class="selector-input" value="${escapeHtml(String(displayValue))}" readonly>`;
                console.log(`Setting selector for field ${fieldData.label || fieldData.match || `Field ${fieldIndex + 1}`} to "${displayValue}"`);
            } else {
                td.textContent = String(displayValue);
            }
            row.appendChild(th); row.appendChild(td);
        }
    });

    // Add defaultOption if present (for radio-group and checkbox-group)
    if (fieldData.defaultOption) {
        const row = tbody.insertRow();
        const th = document.createElement('th');
        const td = document.createElement('td');
        th.textContent = 'DefaultOption';
        td.textContent = fieldData.defaultOption;
        row.appendChild(th); row.appendChild(td);
    }

    // Add options array if present (for radio-group and checkbox-group)
    if (fieldData.options && fieldData.options.length > 0) {
        const row = tbody.insertRow();
        const th = document.createElement('th');
        const td = document.createElement('td');
        th.textContent = 'Options';
        td.textContent = JSON.stringify(fieldData.options);
        row.appendChild(th); row.appendChild(td);
    }

    propertiesTable.appendChild(tbody);
    panelElement.appendChild(propertiesTable);

    // For all field types, add a value container to show the value from the report field
    // This ensures we always have a value to use, even for select, radio, checkbox elements
    const valueContainer = document.createElement('div');
    valueContainer.className = 'value-container';

    // Use the same UI for all field types
    valueContainer.innerHTML = `
        <label for="field-value-${fieldId}">Value (from Report Field: ${escapeHtml(fieldData.match || 'N/A')}):</label>
        <textarea id="field-value-${fieldId}" class="field-value" placeholder="Value to submit..." rows="2" title="Value corresponding to report field: ${escapeHtml(fieldData.match || 'N/A')}">${escapeHtml(fieldData.value || '')}</textarea>
    `;

    panelElement.appendChild(valueContainer);

    // For select, radio, checkbox elements, add an additional note
    if (['select', 'radio-group', 'checkbox-group'].includes(fieldData.type)) {
        const noteElement = document.createElement('p');
        noteElement.textContent = `(Value will be matched to available options for type: ${fieldData.type})`;
        noteElement.style.fontSize = '11px';
        noteElement.style.color = '#6c757d';
        noteElement.style.marginTop = '5px';
        panelElement.appendChild(noteElement);
    }
    fieldElement.appendChild(panelElement);
    return fieldElement;
}


// Add event listener to validate selectors when the submit tab is activated
document.addEventListener('tabActivated', function(event) {
    if (event.detail && event.detail.tabId === 'submit-tab') {
        console.log('UI-Submit: Submit tab activated, validating selectors');
        setTimeout(() => {
            validateSubmitSelectors();
            updateContentScriptWithSelectors();
        }, 300);
    }
});

// Function to collect all selectors and send them to the content script
function updateContentScriptWithSelectors() {
    if (!state.currentTabId) return;

    const selectorInputs = submitFieldsContentUI.querySelectorAll('.selector-input');
    const selectors = [];

    selectorInputs.forEach(input => {
        const selector = input.value.trim();
        // Include all non-empty selectors, even if they're invalid
        // The content script will handle validation
        if (selector) {
            selectors.push(selector);
        }
    });

    console.log(`UI-Submit: Sending ${selectors.length} selectors to content script for hover detection`);

    if (selectors.length > 0) {
        chrome.runtime.sendMessage({
            action: 'proxyRequestToContentScript',
            targetTabId: state.currentTabId,
            contentScriptAction: 'updateKnownSelectors',
            payload: { selectors: selectors }
        }).catch(error => console.error('UI-Submit: Error updating content script selectors:', error));
    } else {
        console.log('UI-Submit: No selectors to send to content script');
    }
}

// Add hover highlight functionality for accordion headers in the submit tab
function setupSubmitSelectorHoverEvents() {
    console.log("Setting up hover events for submit tab accordion headers");

    // Add mouseenter event for accordion headers
    submitFieldsContentUI.addEventListener('mouseenter', (e) => {
        // Check if this is a field header
        if (e.target && e.target.tagName && e.target.tagName.toLowerCase() === 'h3' && e.target.classList.contains('field-header')) {
            // Find the container and then the selector input
            const container = e.target.closest('.dynamic-field-container');
            if (container) {
                // First try to find the field selector
                const selectorInput = container.querySelector('.selector-input');

                // Only highlight if the selector has a value
                if (selectorInput && selectorInput.value.trim()) {
                    chrome.runtime.sendMessage({
                        action: 'proxyRequestToContentScript',
                        targetTabId: state.currentTabId,
                        contentScriptAction: 'highlightElementBySelector',
                        payload: { selector: selectorInput.value.trim(), isValid: true }
                    }).catch(error => console.error('UI-Submit: Error highlighting element on header hover:', error));
                } else {
                    // If no field selector with value, check if there are option selectors in the properties table
                    const propertiesTable = container.querySelector('.properties-table');
                    if (propertiesTable) {
                        // Look for options in the properties table
                        const optionsRow = Array.from(propertiesTable.querySelectorAll('tr')).find(row =>
                            row.querySelector('th') && row.querySelector('th').textContent === 'Options');

                        if (optionsRow) {
                            try {
                                // Parse the options JSON from the text content
                                const optionsText = optionsRow.querySelector('td').textContent;
                                const options = JSON.parse(optionsText);

                                // Find the first option with a non-empty selector
                                const optionWithSelector = options.find(opt => opt.selector && opt.selector.trim());

                                if (optionWithSelector && optionWithSelector.selector) {
                                    chrome.runtime.sendMessage({
                                        action: 'proxyRequestToContentScript',
                                        targetTabId: state.currentTabId,
                                        contentScriptAction: 'highlightElementBySelector',
                                        payload: { selector: optionWithSelector.selector.trim(), isValid: true }
                                    }).catch(error => console.error('UI-Submit: Error highlighting option element on header hover:', error));
                                }
                            } catch (error) {
                                console.error('UI-Submit: Error parsing options JSON:', error);
                            }
                        }
                    }
                }
            }
        }
    }, true); // Use capture phase

    // Add mouseleave event for accordion headers
    submitFieldsContentUI.addEventListener('mouseleave', (e) => {
        if (e.target && e.target.tagName && e.target.tagName.toLowerCase() === 'h3' && e.target.classList.contains('field-header')) {
            // Remove highlight when mouse leaves the header
            chrome.runtime.sendMessage({
                action: 'proxyRequestToContentScript',
                targetTabId: state.currentTabId,
                contentScriptAction: 'removeValidationHighlight',
                payload: {}
            }).catch(error => console.error('UI-Submit: Error removing highlight on header leave:', error));
        }
    }, true); // Use capture phase
}

// Function to find and open the accordion containing a specific selector
function findAndOpenAccordionWithSelector(selector) {
    if (!selector) return false;

    // Find all selector inputs in the submit tab
    const selectorInputs = submitFieldsContentUI.querySelectorAll('.selector-input');

    for (const input of selectorInputs) {
        if (input.value.trim() === selector) {
            // Found matching selector, get its container
            const container = input.closest('.dynamic-field-container');
            if (container) {
                // Get the index of this container among all containers
                const allContainers = Array.from(submitFieldsContentUI.querySelectorAll('.dynamic-field-container'));
                const index = allContainers.indexOf(container);

                if (index !== -1) {
                    // Open the accordion at this index
                    $(submitFieldsContentUI).accordion('option', 'active', index);
                    return true;
                }
            }
        }
    }

    return false;
}

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message) => {
    if (message.action === 'pageElementHovered' && message.selector) {
        // Only process if we're in the submit tab
        const activeTab = document.querySelector('.ui-tabs-active');
        if (activeTab && activeTab.id === 'submit-tab-link') {
            findAndOpenAccordionWithSelector(message.selector);
        }
    }
    return false; // No async response needed
});

// Initialize hover events when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
    setupSubmitSelectorHoverEvents();
});

console.log("sidepanel-ui-submit.js loaded");