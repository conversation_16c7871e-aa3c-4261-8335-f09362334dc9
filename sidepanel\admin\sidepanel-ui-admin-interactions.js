// Function to create a new page accordion
function createPageAccordion(pageIndex, pageTitle = '') {
    const pageId = `page-accordion-${pageIndex}`;
    const pageElement = document.createElement('div');
    pageElement.className = 'page-accordion';
    pageElement.setAttribute('data-page-index', pageIndex);
    pageElement.setAttribute('data-page-id', pageId);

    // Create header with title and controls
    const headerElement = document.createElement('div');
    headerElement.className = 'page-accordion-header';

    const titleSpan = document.createElement('span');
    titleSpan.className = 'page-title';
    titleSpan.textContent = pageTitle || `Page ${pageIndex + 1}`;

    const controlsDiv = document.createElement('div');
    controlsDiv.className = 'page-controls';

    const selectElementsButton = document.createElement('button');
    selectElementsButton.className = 'select-elements-button';
    selectElementsButton.textContent = 'Select Elements';
    selectElementsButton.setAttribute('data-page-id', pageId);

    const addFieldButton = document.createElement('button');
    addFieldButton.className = 'add-field-button';
    addFieldButton.textContent = '+ Add Field';
    addFieldButton.setAttribute('data-page-id', pageId);

    controlsDiv.appendChild(selectElementsButton);
    controlsDiv.appendChild(addFieldButton);

    headerElement.appendChild(titleSpan);
    headerElement.appendChild(controlsDiv);

    // Create content container for fields
    const contentElement = document.createElement('div');
    contentElement.className = 'page-accordion-content';
    contentElement.id = `page-content-${pageId}`;

    // Add fields container
    const fieldsContainer = document.createElement('div');
    fieldsContainer.className = 'page-fields-container';
    fieldsContainer.id = `fields-container-${pageId}`;

    contentElement.appendChild(fieldsContainer);

    // Assemble the page accordion
    pageElement.appendChild(headerElement);
    pageElement.appendChild(contentElement);

    return pageElement;
}

// Function to initialize page accordions
function initializePageAccordions(adminFieldsContentUI) {
    if (!adminFieldsContentUI) {
        adminFieldsContentUI = document.getElementById('admin-form-fields-content');
        if (!adminFieldsContentUI) {
            return;
        }
    }

    const pageAccordions = adminFieldsContentUI.querySelectorAll('.page-accordion');

    pageAccordions.forEach((pageAccordion) => {
        const header = pageAccordion.querySelector('.page-accordion-header');
        const content = pageAccordion.querySelector('.page-accordion-content');

        // Toggle content visibility when header is clicked
        header.addEventListener('click', (event) => {
            // Don't toggle if clicking on a button
            if (event.target.tagName === 'BUTTON') {
                return;
            }

            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        });

        // Initialize field accordions within this page
        const fieldsContainer = pageAccordion.querySelector('.page-fields-container');
        if (fieldsContainer) {
            initializeAdminAccordionAndSortable(fieldsContainer);
        }

        // Set up event listeners for page buttons
        const selectElementsButton = pageAccordion.querySelector('.select-elements-button');
        if (selectElementsButton) {
            selectElementsButton.addEventListener('click', () => {
                handleSelectElementsForPage(pageAccordion.getAttribute('data-page-id'));
            });
        }

        const addFieldButton = pageAccordion.querySelector('.add-field-button');
        if (addFieldButton) {
            addFieldButton.addEventListener('click', () => {
                handleAddFieldToPage(pageAccordion.getAttribute('data-page-id'));
            });
        }
    });
}

function initializeAdminAccordionAndSortable(adminFieldsContentUI) {
    if (!adminFieldsContentUI) {
        adminFieldsContentUI = document.getElementById('admin-form-fields-content');
        if (!adminFieldsContentUI) {
            return;
        }
    }

    const $adminFieldsContent = $(adminFieldsContentUI);

    // Check if this is a page fields container - used for context in future enhancements

    const fieldContainers = adminFieldsContentUI.querySelectorAll('.dynamic-field-container');
    fieldContainers.forEach(container => {
        const header = container.querySelector('h3');
        if (header && !header.classList.contains('ui-accordion-header')) {
            header.classList.add('ui-accordion-header', 'ui-corner-top');
        }

        const panel = container.querySelector('.field-panel');
        if (panel && !panel.classList.contains('ui-accordion-content')) {
            panel.classList.add('ui-accordion-content', 'ui-corner-bottom');
        }

        const optionsContainer = container.querySelector('.options-container');
        if (optionsContainer && optionsContainer.id) {
            initializeAdminOptionsAccordionAndSortable(optionsContainer);
        }
    });

    try { if ($adminFieldsContent.hasClass('ui-accordion')) $adminFieldsContent.accordion('destroy'); } catch(e){ }
    try { if ($adminFieldsContent.hasClass('ui-sortable')) $adminFieldsContent.sortable('destroy'); } catch(e){ }

    const allSelectorInputs = adminFieldsContentUI.querySelectorAll('.field-selector, .option-selector');
    allSelectorInputs.forEach(input => {
        input.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        if (typeof updateAccordionHeaderState === 'function') {
            updateAccordionHeaderState(input);
        } else {
            const container = input.closest('.dynamic-field-container, .option-item');
            if (container) {
                container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
            }
        }
    });

    if (typeof updateAllAccordionHeaderStates === 'function') {
        updateAllAccordionHeaderStates(false);
    }

    $adminFieldsContent.accordion({
        header: "> div.dynamic-field-container > h3",
        collapsible: true, heightStyle: "content", active: false, icons: false,
        activate: function(_, ui) {
            if (ui.newPanel && ui.newPanel.length) {
                const selectorInputs = ui.newPanel.find('.field-selector, .option-selector');
                selectorInputs.each(function() {
                    highlightSelectorElement(this, false);
                });
            }
        },
        create: function() {
            if (typeof updateAllAccordionHeaderStates === 'function') {
                updateAllAccordionHeaderStates(false);
            }
        }
    });

    $adminFieldsContent.sortable({
        handle: "h3", axis: "y", containment: "parent",
        items: "> div.dynamic-field-container",
        stop: function(_, ui) {
            ui.item.children("h3").triggerHandler("focusout");
            $adminFieldsContent.children('.dynamic-field-container').each(function(newIndex) {
                const fieldContainer = $(this);
                const oldIndex = parseInt(fieldContainer.attr('data-field-index'));
                if (oldIndex === newIndex) return;

                const oldFieldId = `dynamic-field-${oldIndex}`;
                const newFieldId = `dynamic-field-${newIndex}`;

                fieldContainer.attr('data-field-index', newIndex);
                fieldContainer.attr('data-field-id', newFieldId);

                fieldContainer.find('[id^="' + oldFieldId + '"]').each(function() { this.id = this.id.replace(oldFieldId, newFieldId); });
                fieldContainer.find('label[for^="' + oldFieldId + '"]').each(function() { $(this).attr('for', $(this).attr('for').replace(oldFieldId, newFieldId)); });

                fieldContainer.find('[data-field-index="' + oldIndex + '"]').attr('data-field-index', newIndex);
                fieldContainer.find('[data-field-id="' + oldFieldId + '"]').attr('data-field-id', newFieldId);
                fieldContainer.find('.generate-selector-path-button[data-target-selector^="#field-selector-' + oldFieldId + '"]').attr('data-target-selector', `#field-selector-${newFieldId}`);
                fieldContainer.find('.add-option-button[data-field-id="' + oldFieldId + '"]').attr('data-field-id', newFieldId);
                fieldContainer.find('.remove-field-button[data-field-index="' + oldIndex + '"]').attr('data-field-index', newIndex);

                const optionsContainer = fieldContainer.find('.options-container[id^="options-container-' + oldFieldId + '"]');
                if (optionsContainer.length) {
                    const newOptionsContainerId = `options-container-${newFieldId}`;
                    optionsContainer.attr('id', newOptionsContainerId);

                    optionsContainer.children('.option-item').each(function(optionIndex) {
                        const optionItem = $(this);
                        const oldOptionIndex = parseInt(optionItem.attr('data-option-index'));

                        const oldOptionIdPrefix = `${oldFieldId}-option-${oldOptionIndex}`;
                        const newOptionIdPrefix = `${newFieldId}-option-${optionIndex}`;

                        optionItem.attr('data-option-index', optionIndex);

                        optionItem.find('[id^="' + oldOptionIdPrefix + '"]').each(function() { this.id = this.id.replace(oldOptionIdPrefix, newOptionIdPrefix); });
                        optionItem.find('label[for^="' + oldOptionIdPrefix + '"]').each(function() { $(this).attr('for', $(this).attr('for').replace(oldOptionIdPrefix, newOptionIdPrefix)); });

                        optionItem.find('[data-option-index="' + oldOptionIndex + '"]').attr('data-option-index', optionIndex);
                        optionItem.find('[data-option-id^="' + oldOptionIdPrefix + '"]').each(function() { $(this).attr('data-option-id', $(this).attr('data-option-id').replace(oldOptionIdPrefix, newOptionIdPrefix)); });
                        optionItem.find('[data-field-id="' + oldFieldId + '"]').attr('data-field-id', newFieldId);

                        optionItem.find('.generate-selector-path-button[data-target-selector^="#option-selector-' + oldOptionIdPrefix + '"]').attr('data-target-selector', `#option-selector-${newOptionIdPrefix}`);
                        optionItem.find('.remove-option-button[data-field-id="' + oldFieldId + '"][data-option-index="' + oldOptionIndex + '"]').attr({'data-field-id': newFieldId, 'data-option-index': optionIndex}); // Update remove button attributes
                    });
                    initializeAdminOptionsAccordionAndSortable(optionsContainer[0]); // Re-initialize sortable/accordion for options
                }
                // After updating all IDs/attributes for the field and its options, update the default option dropdown
                updateDefaultOptionDropdown(newFieldId);
            });
            $adminFieldsContent.accordion("refresh");
            $adminFieldsContent.sortable("refresh");
        }
    }).disableSelection();
}


// Handler for adding a new page accordion
function handleAddPage() {
    const adminFieldsContentUI = document.getElementById('admin-form-fields-content');
    if (!adminFieldsContentUI) {
        return;
    }

    // Remove placeholder if it exists
    const placeholder = adminFieldsContentUI.querySelector('p.loading-placeholder');
    if (placeholder) placeholder.remove();

    // Get the current number of page accordions
    const pageCount = adminFieldsContentUI.querySelectorAll('.page-accordion').length;

    // Create a new page accordion
    const pageElement = createPageAccordion(pageCount);
    adminFieldsContentUI.appendChild(pageElement);

    // Initialize the page accordion
    initializePageAccordions(adminFieldsContentUI);

    // Open the new page accordion
    const content = pageElement.querySelector('.page-accordion-content');
    if (content) {
        content.style.display = 'block';
    }

    // Scroll to the new page
    pageElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Handler for selecting elements for a specific page
function handleSelectElementsForPage(pageId) {
    // Store the current page ID for later use when elements are selected
    window.currentPageId = pageId;

    // Also store the page ID in the state object for selector generation
    if (state && typeof state === 'object') {
        state.currentSelectorPageId = pageId;
    }

    // Find and disable the specific "Select Elements" button for this page
    const pageAccordion = document.querySelector(`.page-accordion[data-page-id="${pageId}"]`);
    const selectElementsButton = pageAccordion?.querySelector('.select-elements-button');
    if (selectElementsButton) {
        selectElementsButton.disabled = true;
        selectElementsButton.textContent = 'Selecting...';
    }

    // Call the existing select element function
    if (typeof handleSelectElementClick === 'function') {
        handleSelectElementClick();
    } else {
        console.error("handleSelectElementClick function not found");
        // Re-enable button on error
        if (selectElementsButton) {
            selectElementsButton.disabled = false;
            selectElementsButton.textContent = 'Select Elements';
        }
    }
}

// Handler for adding a field to a specific page
function handleAddFieldToPage(pageId) {
    const fieldsContainer = document.getElementById(`fields-container-${pageId}`);
    if (!fieldsContainer) {
        return;
    }

    // Get the next field index for this page
    const nextIndex = Array.from(fieldsContainer.children).filter(el => el.matches('.dynamic-field-container')).length;

    // Create a new field element
    const newField = createAdminFormFieldElement(nextIndex, {});

    // Add the page ID to the field
    newField.setAttribute('data-page-id', pageId);

    // Add the field to the page
    fieldsContainer.appendChild(newField);

    // Initialize the field accordion
    initializeAdminAccordionAndSortable(fieldsContainer);



    // Scroll to the new field
    newField.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

    // Open the new field accordion
    $(fieldsContainer).accordion('option', 'active', nextIndex);
}

function initializeAdminOptionsAccordionAndSortable(optionsContainerElement) {
    if (!optionsContainerElement?.id) {
        return;
    }

    const optionItems = optionsContainerElement.querySelectorAll('.option-item');

    optionItems.forEach((item) => {
        const header = item.querySelector('h4');
        if (header && !header.classList.contains('ui-accordion-header')) {
            header.classList.add('ui-accordion-header', 'ui-corner-top');
        }

        const panel = item.querySelector('.option-panel');
        if (panel && !panel.classList.contains('ui-accordion-content')) {
            panel.classList.add('ui-accordion-content', 'ui-corner-bottom');
        }
    });

    const $optionsContainer = $(optionsContainerElement);
    const fieldId = optionsContainerElement.closest('.dynamic-field-container')?.getAttribute('data-field-id') || optionsContainerElement.id.replace('options-container-', '');

    try { if ($optionsContainer.hasClass('ui-accordion')) $optionsContainer.accordion('destroy'); } catch (e) { }
    try { if ($optionsContainer.hasClass('ui-sortable')) $optionsContainer.sortable('destroy'); } catch (e) { }

    const optionSelectors = optionsContainerElement.querySelectorAll('.option-selector');

    optionSelectors.forEach((input) => {
        input.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        if (typeof updateAccordionHeaderState === 'function') {
            updateAccordionHeaderState(input);
        } else {
            const container = input.closest('.option-item');
            if (container) {
                container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
            }
        }
    });

    optionSelectors.forEach(selectorInput => {
        if (typeof updateAccordionHeaderState === 'function') {
            updateAccordionHeaderState(selectorInput);
        }
    });

    $optionsContainer.accordion({
        header: "> div.option-item > h4",
        collapsible: true, heightStyle: "content", active: false, icons: false,
        activate: function(_, ui) {
            if (ui.newPanel && ui.newPanel.length) {
                const selectorInputs = ui.newPanel.find('.option-selector');
                selectorInputs.each(function() {
                    highlightSelectorElement(this, false);
                });


            }
        },
        create: function() {
            const optionSelectors = optionsContainerElement.querySelectorAll('.option-selector');
            optionSelectors.forEach(selectorInput => {
                if (typeof updateAccordionHeaderState === 'function') {
                    updateAccordionHeaderState(selectorInput);
                }
            });
        }
    });

    $optionsContainer.sortable({
        handle: "h4", axis: "y", containment: "parent",
        items: "> div.option-item",
        stop: function(_, ui) {
            ui.item.children("h4").triggerHandler("focusout");
            $optionsContainer.children('.option-item').each(function(newIndex) {
                 const optionItem = $(this);
                 const oldIndex = parseInt(optionItem.attr('data-option-index'));
                 if (oldIndex === newIndex) return;


                 const oldOptionIdPrefix = `${fieldId}-option-${oldIndex}`;
                 const newOptionIdPrefix = `${fieldId}-option-${newIndex}`;

                 optionItem.attr('data-option-index', newIndex);


                 optionItem.find('[id^="' + oldOptionIdPrefix + '"]').each(function() { this.id = this.id.replace(oldOptionIdPrefix, newOptionIdPrefix); });
                 optionItem.find('label[for^="' + oldOptionIdPrefix + '"]').each(function() { $(this).attr('for', $(this).attr('for').replace(oldOptionIdPrefix, newOptionIdPrefix)); });


                 optionItem.find('[data-option-index="' + oldIndex + '"]').attr('data-option-index', newIndex);
                 optionItem.find('[data-option-id^="' + oldOptionIdPrefix + '"]').each(function() { $(this).attr('data-option-id', $(this).attr('data-option-id').replace(oldOptionIdPrefix, newOptionIdPrefix)); });

                 optionItem.find('[data-field-id]').attr('data-field-id', fieldId);
                 optionItem.find('.generate-selector-path-button[data-target-selector^="#option-selector-' + oldOptionIdPrefix + '"]').attr('data-target-selector', `#option-selector-${newOptionIdPrefix}`);

                 optionItem.find('.remove-option-button[data-field-id="' + fieldId + '"][data-option-index="' + oldIndex + '"]').attr('data-option-index', newIndex);
            });
            $optionsContainer.accordion("refresh");
            $optionsContainer.sortable("refresh");


            updateDefaultOptionDropdown(fieldId);
        }
    }).disableSelection();

    updateDefaultOptionDropdown(fieldId);

    setTimeout(() => {
        updateDefaultOptionDropdown(fieldId);
    }, 200);
}

