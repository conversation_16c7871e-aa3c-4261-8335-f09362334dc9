// sidepanel-ui-tools.js

console.log("sidepanel-ui-tools.js loading");

const toolsTabContent = document.getElementById('tools-tab');

// Import click-utils.js if it's not already loaded
if (typeof window.clickUtils === 'undefined') {
    console.log("UI-Tools: Loading click-utils.js");
    const script = document.createElement('script');
    script.src = '/sidepanel/common/click-utils.js';
    document.head.appendChild(script);
}

// Function to initialize the Tools tab UI
function initializeToolsUI() {
    console.log("UI-Tools: Initializing Tools tab UI");

    // Create the selector click tool section
    createSelectorClickTool();

    // Create the XPath to CSS converter tool section
    createXPathToCssConverterTool();
}

// Function to create the selector click tool
function createSelectorClickTool() {
    // Create section header
    const sectionHeader = document.createElement('h4');
    sectionHeader.className = 'section-header';
    sectionHeader.textContent = 'Element Selector Click Tool';

    // Create field group for the selector input
    const fieldGroup = document.createElement('div');
    fieldGroup.className = 'field-group';

    // Create label
    const label = document.createElement('label');
    label.setAttribute('for', 'selector-click-input');
    label.textContent = 'CSS Selector';

    // Create input group with buttons
    const inputGroup = document.createElement('div');
    inputGroup.className = 'field-group-with-button';

    // Create input field
    const input = document.createElement('input');
    input.type = 'text';
    input.id = 'selector-click-input';
    input.name = 'selector-click-input';
    input.placeholder = 'Enter CSS selector (e.g., #submit-button)';

    // Create copy button
    const copyButton = document.createElement('button');
    copyButton.className = 'field-button copy-button';
    copyButton.innerHTML = '<svg><use href="#icon-copy"></use></svg>';
    copyButton.title = 'Copy CSS selector to clipboard';

    // Create click button
    const clickButton = document.createElement('button');
    clickButton.className = 'field-button click-button';
    clickButton.textContent = 'Click';
    clickButton.title = 'Click element with this selector';

    // Assemble the components
    inputGroup.appendChild(input);
    inputGroup.appendChild(copyButton);
    inputGroup.appendChild(clickButton);

    fieldGroup.appendChild(label);
    fieldGroup.appendChild(inputGroup);

    // Add to the tools tab content
    toolsTabContent.appendChild(sectionHeader);
    toolsTabContent.appendChild(fieldGroup);
}

// Function to handle the click button action
// Make it globally accessible so it can be called from sidepanel-main-tools.js
window.handleSelectorClick = function(selector) {
    if (!state.currentTabId) {
        alert("No active tab detected. Please refresh the extension.");
        return;
    }

    if (!selector || selector.trim() === '') {
        alert("Please enter a valid CSS selector.");
        return;
    }

    console.log(`UI-Tools: Attempting to click element with selector: "${selector}"`);

    // Use clickUtils to click the element
    console.log(`UI-Tools: Using clickUtils.clickElement for selector: ${selector}`);

    window.clickUtils.clickElement(selector.trim(), state.currentTabId)
        .then(response => {
            if (response && response.success) {
                console.log(`UI-Tools: Successfully clicked element with selector: "${selector}"`);
            } else {
                const errorMsg = response?.error || 'Unknown error';
                console.error(`UI-Tools: Failed to click element: ${errorMsg}`);
                alert(`Failed to click element: ${errorMsg}`);
            }
        })
        .catch(error => {
            console.error('UI-Tools: Error sending click request:', error);
            alert(`Error: ${error.message}`);
        });
}

// Function to handle the copy button action
// Make it globally accessible so it can be called from sidepanel-main-tools.js
window.handleSelectorCopy = function(selector) {
    if (!selector || selector.trim() === '') {
        alert("No CSS selector to copy. Please enter a selector first.");
        return;
    }

    console.log(`UI-Tools: Copying CSS selector to clipboard: "${selector}"`);

    // Use the modern Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(selector.trim())
            .then(() => {
                console.log(`UI-Tools: Successfully copied CSS selector to clipboard: "${selector}"`);
                // Optional: Show a brief success indication
                // You could add a temporary visual feedback here
            })
            .catch(error => {
                console.error('UI-Tools: Error copying to clipboard:', error);
                // Fallback to the older method
                fallbackCopyToClipboard(selector.trim());
            });
    } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyToClipboard(selector.trim());
    }
}

// Fallback copy function for older browsers
function fallbackCopyToClipboard(text) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // Execute the copy command
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            console.log(`UI-Tools: Successfully copied CSS selector using fallback method: "${text}"`);
        } else {
            throw new Error('Copy command failed');
        }
    } catch (error) {
        console.error('UI-Tools: Fallback copy failed:', error);
        alert(`Failed to copy to clipboard: ${error.message}`);
    }
}

// Function to create the XPath to CSS converter tool
function createXPathToCssConverterTool() {
    // Create section header with some spacing from previous tool
    const sectionHeader = document.createElement('h4');
    sectionHeader.className = 'section-header';
    sectionHeader.style.marginTop = '20px';
    sectionHeader.textContent = 'XPath to CSS Converter';

    // Create field group for the XPath input
    const fieldGroup = document.createElement('div');
    fieldGroup.className = 'field-group';

    // Create label for XPath input
    const xpathLabel = document.createElement('label');
    xpathLabel.setAttribute('for', 'xpath-input');
    xpathLabel.textContent = 'XPath Expression';

    // Create input group with button for XPath
    const xpathInputGroup = document.createElement('div');
    xpathInputGroup.className = 'field-group-with-button';

    // Create XPath input field
    const xpathInput = document.createElement('input');
    xpathInput.type = 'text';
    xpathInput.id = 'xpath-input';
    xpathInput.name = 'xpath-input';
    xpathInput.placeholder = 'Enter XPath (e.g., /html/body/div/div/main/div[3]/img)';

    // Create convert button
    const convertButton = document.createElement('button');
    convertButton.className = 'field-button convert-button';
    convertButton.textContent = 'Convert';
    convertButton.title = 'Convert XPath to CSS selector';

    // Create field group for the CSS output
    const cssOutputGroup = document.createElement('div');
    cssOutputGroup.className = 'field-group';

    // Create label for CSS output
    const cssLabel = document.createElement('label');
    cssLabel.setAttribute('for', 'css-output');
    cssLabel.textContent = 'CSS Selector';

    // Create CSS output field
    const cssOutput = document.createElement('input');
    cssOutput.type = 'text';
    cssOutput.id = 'css-output';
    cssOutput.name = 'css-output';
    cssOutput.readOnly = true;
    cssOutput.placeholder = 'CSS selector will appear here';

    // Assemble the components
    xpathInputGroup.appendChild(xpathInput);
    xpathInputGroup.appendChild(convertButton);

    fieldGroup.appendChild(xpathLabel);
    fieldGroup.appendChild(xpathInputGroup);

    cssOutputGroup.appendChild(cssLabel);
    cssOutputGroup.appendChild(cssOutput);

    // Add to the tools tab content
    toolsTabContent.appendChild(sectionHeader);
    toolsTabContent.appendChild(fieldGroup);
    toolsTabContent.appendChild(cssOutputGroup);
}

// Function to handle the XPath to CSS conversion
window.handleXPathToCssConversion = function(xpath) {
    if (!xpath || xpath.trim() === '') {
        alert("Please enter a valid XPath expression.");
        return;
    }

    console.log(`UI-Tools: Converting XPath to CSS: "${xpath}"`);

    try {
        // Use the xpathToCssSelector function from xpath-to-css.js
        const cssSelector = window.xpathToCssSelector(xpath.trim());

        // Update the CSS output field
        const cssOutput = document.getElementById('css-output');
        if (cssOutput) {
            cssOutput.value = cssSelector;

            // Select the text for easy copying
            cssOutput.select();

            console.log(`UI-Tools: XPath converted to CSS: "${cssSelector}"`);
        } else {
            console.error("UI-Tools: CSS output field not found");
            alert("Error: CSS output field not found");
        }
    } catch (error) {
        console.error('UI-Tools: Error converting XPath to CSS:', error);
        alert(`Error converting XPath: ${error.message}`);
    }
}



// Add event listener to validate selectors when the tools tab is activated
document.addEventListener('tabActivated', function(event) {
    if (event.detail && event.detail.tabId === 'tools-tab') {
        console.log('UI-Tools: Tools tab activated');
    }
});

// Initialize the Tools tab UI when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (toolsTabContent) {
        initializeToolsUI();
    } else {
        console.error("UI-Tools: Tools tab content element not found!");
    }
});

console.log("sidepanel-ui-tools.js loaded");
