// sidepanel-main-admin.js

console.log("sidepanel-main-admin.js loading");

const saveJsonButtonMain = document.getElementById('saveJsonButton');
const addPageButtonMain = document.getElementById('addPageButton');
const configurationReadyCheckbox = document.getElementById('configurationReady');
const configurationReviewedCheckbox = document.getElementById('configurationReviewed');
const adminTabContent = document.getElementById('admin-tab');
// const adminFieldsContentUI = document.getElementById('admin-form-fields-content'); // REMOVE THIS LINE

// Function to immediately apply missing-selector class to empty inputs and their containers
// Added a flag to track if we've already applied classes to avoid duplicates
let initialClassesApplied = false;

function applyInitialSelectorClasses() {
    // If we've already applied classes, don't do it again
    if (initialClassesApplied) {
        console.log("Initial selector classes already applied, skipping");
        return;
    }

    console.log("Main-Admin: Removing validation classes");

    // Find all selector inputs
    const allSelectorInputs = document.querySelectorAll('.field-selector, .option-selector');

    allSelectorInputs.forEach(input => {
        // Remove all validation classes from inputs
        input.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        // Remove all validation classes from containers
        const container = input.closest('.dynamic-field-container, .option-item');
        if (container) {
            container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
            console.log('Main-Admin: Removed validation classes from container:', container);
        }
    });

    // Mark that we've applied classes
    initialClassesApplied = true;
}



function setupAdminEventListeners() {
    console.log("Main-Admin: Setting up admin event listeners...");
    saveJsonButtonMain?.addEventListener('click', handleSaveSiteJsonClick);
    addPageButtonMain?.addEventListener('click', handleAddPage);

    // Add event listeners for the status checkboxes to save state immediately
    configurationReadyCheckbox?.addEventListener('change', handleConfigurationReadyChange);
    configurationReviewedCheckbox?.addEventListener('change', handleConfigurationReviewedChange);

    // Apply initial selector classes when the admin tab is shown
    $(document).on('tabsactivate', function(_, ui) {
        if (ui.newPanel.attr('id') === 'admin-tab') {
            console.log("Main-Admin: Admin tab activated, applying initial selector classes");
            // Reset the flag when switching to the admin tab to ensure classes are applied
            initialClassesApplied = false;
            setTimeout(applyInitialSelectorClasses, 0);
        }
    });

    // Also apply initial selector classes on page load
    setTimeout(applyInitialSelectorClasses, 0);

    adminTabContent?.addEventListener('click', (event) => {
        const target = event.target;
        const button = target.closest('button');

        if (!button) return;

        // Prevent event from being handled multiple times
        event.stopPropagation();

        // Check button classes in order of specificity
        if (button.matches('.generate-selector-path-button')) {
            console.log('Handling generate selector path button click');
            handleGenerateSelectorPathClick(button, event);
        } else if (button.matches('.remove-field-button')) {
            console.log('Handling remove field button click');
            handleRemoveFieldClick(button);
        } else if (button.matches('.add-option-button')) {
            console.log('Handling add option button click');
            handleAddOptionClick(button);
        } else if (button.matches('.remove-option-button')) {
            console.log('Handling remove option button click');
            handleRemoveOptionClick(button);
        }
    });
     console.log("Main-Admin: Admin event listeners setup complete.");
}

/**
 * Handles changes to the "Configuration Ready" checkbox
 */
async function handleConfigurationReadyChange() {
    if (!state.currentSiteId) {
        return;
    }

    try {
        const isReady = configurationReadyCheckbox?.checked || false;
        const readyStateKey = `configReady_${state.currentSiteId}`;
        chrome.storage.local.set({ [readyStateKey]: isReady });
        console.log(`Configuration ready state saved: ${isReady} for site ${state.currentSiteId}`);
    } catch (error) {
        console.error('Error saving configuration ready state:', error);
    }
}

/**
 * Handles changes to the "Configuration Reviewed" checkbox
 */
async function handleConfigurationReviewedChange() {
    if (!state.currentSiteId) {
        return;
    }

    try {
        const isReviewed = configurationReviewedCheckbox?.checked || false;
        const reviewedStateKey = `configReviewed_${state.currentSiteId}`;
        chrome.storage.local.set({ [reviewedStateKey]: isReviewed });
        console.log(`Configuration reviewed state saved: ${isReviewed} for site ${state.currentSiteId}`);
    } catch (error) {
        console.error('Error saving configuration reviewed state:', error);
    }
}

async function handleSelectElementClick() {
    if (!state.currentTabId) {
        alert('No active tab identified.');
        return;
    }

    // Set global flag to indicate selection mode is active
    window.isSelectionModeActiveInSidepanel = true;

    // Since we removed the main select button, we don't need to disable it
    // The individual "Select Elements" buttons in each page will handle their own state
    state.isGeneratingSelectorFor = null;

    // Reset the page ID for selector generation when using the main Select Element button
    // This ensures we're not targeting a specific page unless explicitly set
    state.currentSelectorPageId = null;

    // Make sure the selector generation mode flag is OFF
    // This ensures AI processing will happen for the main Select Element button
    window.isSelectorGenerationMode = false;

    // Add a timeout to automatically reset the state if no response is received
    const buttonResetTimeout = setTimeout(() => {
        resetSelectButtons();
        state.isGeneratingSelectorFor = null;
        state.currentSelectorPageId = null;
        window.isSelectorGenerationMode = false;
        window.isSelectionModeActiveInSidepanel = false;
    }, 10000); // 10 seconds timeout

    // Maximum number of retries
    const maxRetries = 3; // Increase retries
    let retryCount = 0;
    let success = false;

    while (retryCount <= maxRetries && !success) {
        try {
            if (retryCount > 0) {
                // Short delay before retry
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'enableSelectionMode',
                    payload: {
                        isSelectorGenerationOnly: false // This indicates we want full processing with AI
                    }
                });

                if (response?.success === false) {
                    throw new Error(response.error || 'Failed to enable selection mode in content script.');
                }
            } catch (err) {
                // Handle specific error for extension context invalidated
                if (err.message && err.message.includes("Extension context invalidated")) {
                    throw new Error("Extension was reloaded. Please try again.");
                } else {
                    throw err; // Re-throw other errors to be caught by the outer catch
                }
            }

            success = true;
            // Clear the timeout since we succeeded
            clearTimeout(buttonResetTimeout);
        } catch (error) {
            console.error(`Error initiating selection mode:`, error);

            // Add a delay between retries that increases with each retry
            const retryDelay = (retryCount + 1) * 500; // 500ms, 1000ms, 1500ms
            await new Promise(resolve => setTimeout(resolve, retryDelay));

            if (retryCount >= maxRetries) {
                // Clear the timeout since we're handling the reset here
                clearTimeout(buttonResetTimeout);

                // Reset buttons without showing an error message
                resetSelectButtons();
                state.isGeneratingSelectorFor = null;
                state.currentSelectorPageId = null;
                window.isSelectionModeActiveInSidepanel = false;
            }

            retryCount++;
        }
    }
}

async function handleSaveSiteJsonClick() {
    console.log('Main-Admin: Save Site JSON clicked.');

    // Check if we have a site ID
    if (!state.currentSiteId) {
        // Try to load site data for the current URL first
        if (state.currentUrl) {
            try {
                console.log('Main-Admin: Attempting to load site data for URL:', state.currentUrl);
                const result = await getSiteJSON(state.currentUrl);
                if (result.status === 'success' && result.site_id) {
                    state.currentSiteId = result.site_id;
                    state.currentSiteUrl = result.site_url;
                    updateUnifiedStatus();
                    console.log('Main-Admin: Successfully retrieved site ID:', state.currentSiteId);
                } else {
                    console.log('Main-Admin: No site found for URL:', state.currentUrl);
                    alert('Cannot determine current site ID. This URL may not be in the database yet. Please ensure the page is loaded and site ID is available.');
                    return;
                }
            } catch (error) {
                console.error('Main-Admin: Error loading site data:', error);
                alert('Error loading site data: ' + error.message);
                return;
            }
        } else {
            alert('Cannot determine current site ID. Please ensure the page is loaded and site ID is available.');
            return;
        }
    }

    saveJsonButtonMain.disabled = true;
    saveJsonButtonMain.textContent = 'Saving...';
    try {
        const adminData = generateAdminFieldsJSON();
        const currentJson = adminData.fields; // Extract just the fields for the API
        const result = await saveSiteJSON(state.currentSiteId, currentJson);

        if (result.status !== 'success') {
            throw new Error(result.message || 'API Error saving site JSON');
        }

        // Update the state with the current JSON
        state.siteFormData = currentJson;

        // Save both status states to local storage using the site ID as key
        if (state.currentSiteId) {
            const readyStateKey = `configReady_${state.currentSiteId}`;
            const reviewedStateKey = `configReviewed_${state.currentSiteId}`;
            chrome.storage.local.set({
                [readyStateKey]: adminData.configurationReady,
                [reviewedStateKey]: adminData.configurationReviewed
            });
        }

        // Update the submit tab fields with the new data
        updateSubmitFormFields(currentJson);

        alert(`Site JSON saved successfully! ${result.message || ''}`);
        if (result.site_id) {
            state.currentSiteId = result.site_id;
            updateUnifiedStatus();
        }
    } catch (error) {
        console.error('Main-Admin: Error saving site JSON:', error);
        alert(`Save Error: ${error.message}`);
    } finally {
        saveJsonButtonMain.disabled = false;
        saveJsonButtonMain.textContent = 'Save Site JSON';
    }
}

function handleAddFormFieldClick() {
    console.log('Main-Admin: Add Field clicked.');
    // Access adminFieldsContentUI defined in ui-admin.js
    if (!adminFieldsContentUI) {
         console.error("Cannot add field: Admin content container not found.");
         return;
    }
    const placeholder = adminFieldsContentUI.querySelector('p.loading-placeholder'); // Use class
    if (placeholder) placeholder.remove();

    const nextIndex = Array.from(adminFieldsContentUI.children).filter(el => el.matches('.dynamic-field-container')).length;
    const newField = createAdminFormFieldElement(nextIndex, {});
    adminFieldsContentUI.appendChild(newField);

    // No longer applying validation classes to new fields
    const selectorInput = newField.querySelector('.field-selector');
    if (selectorInput) {
        // Remove any validation classes that might be present
        selectorInput.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        // Remove any validation classes from the container
        newField.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
        console.log('Main-Admin: Removed validation classes from new field container');
    }

    initializeAdminAccordionAndSortable(adminFieldsContentUI);

    newField.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    $(adminFieldsContentUI).accordion('option', 'active', nextIndex);
}

async function handleGenerateSelectorPathClick(button, evt) {
    // Prevent event bubbling to avoid triggering other handlers
    if (evt) {
        evt.preventDefault();
        evt.stopPropagation();
    }

    const targetSelectorId = button.getAttribute('data-target-selector');
    if (!targetSelectorId) {
        console.error("Generate button missing target selector ID.");
        return;
    }

    if (!state.currentTabId) {
        alert('Cannot generate selector: Active tab not identified.');
        return;
    }

    button.disabled = true;
    button.innerHTML = '...';

    const targetInputId = targetSelectorId.startsWith('#') ? targetSelectorId.substring(1) : targetSelectorId;

    // Find the page ID that contains this selector input
    const fieldContainer = button.closest('.dynamic-field-container');
    const pageId = fieldContainer ? fieldContainer.getAttribute('data-page-id') : null;

    // Set the state to indicate we're generating a selector path ONLY (no AI processing)
    state.isGeneratingSelectorFor = targetInputId;

    // Store the page ID in the state for use when populating the selector
    state.currentSelectorPageId = pageId;

    // Set global flag to indicate selection mode is active
    window.isSelectionModeActiveInSidepanel = true;

    // Set a special flag to completely prevent AI API calls
    // This will be checked in multiple places to ensure no AI processing happens
    window.isSelectorGenerationMode = true;

    // Add a timeout to automatically reset the button if no response is received
    const buttonResetTimeout = setTimeout(() => {
        if (button.innerHTML === '...') {
            button.disabled = false;
            button.innerHTML = '<svg><use href="#icon-generate"></use></svg>';
            state.isGeneratingSelectorFor = null;
            state.currentSelectorPageId = null;
            window.isSelectorGenerationMode = false;
            window.isSelectionModeActiveInSidepanel = false;
        }
    }, 10000); // 10 seconds timeout

    // Maximum number of retries
    const maxRetries = 3; // Increase retries
    let retryCount = 0;
    let success = false;

    while (retryCount <= maxRetries && !success) {
        try {
            if (retryCount > 0) {
                // Short delay before retry
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'enableSelectionMode',
                    payload: {
                        isSelectorGenerationOnly: true // This is the key flag that indicates we're only generating a selector
                    }
                });

                if (response?.success === false) {
                    throw new Error(response.error || 'Failed to enable selection mode.');
                }
            } catch (err) {
                // Handle specific error for extension context invalidated
                if (err.message && err.message.includes("Extension context invalidated")) {
                    throw new Error("Extension was reloaded. Please try again.");
                } else {
                    throw err; // Re-throw other errors to be caught by the outer catch
                }
            }

            success = true;
            // Clear the timeout since we succeeded
            clearTimeout(buttonResetTimeout);
        } catch (error) {
            console.error(`Error initiating selector generation:`, error);

            // Add a delay between retries that increases with each retry
            const retryDelay = (retryCount + 1) * 500; // 500ms, 1000ms, 1500ms
            await new Promise(resolve => setTimeout(resolve, retryDelay));

            if (retryCount >= maxRetries) {
                // Clear the timeout since we're handling the reset here
                clearTimeout(buttonResetTimeout);

                // Reset the button without showing an error message
                button.disabled = false;
                button.innerHTML = '<svg><use href="#icon-generate"></use></svg>';
                state.isGeneratingSelectorFor = null;
                state.currentSelectorPageId = null;

                // Make sure to reset the selector generation mode flag on error
                window.isSelectorGenerationMode = false;
                window.isSelectionModeActiveInSidepanel = false;
            }

            retryCount++;
        }
    }
}


function handleRemoveFieldClick(button) {
    const fieldContainer = button.closest('.dynamic-field-container');
    if (!fieldContainer) return;

    const fieldLabel = fieldContainer.querySelector('h3 > span')?.textContent || `this field`;
    if (confirm(`Are you sure you want to delete the field "${fieldLabel}"? This cannot be undone.`)) {
        console.log("Main-Admin: Removing field element.");
        fieldContainer.remove();
        initializeAdminAccordionAndSortable();
    }
}

function handleAddOptionClick(button) {
     const fieldId = button.getAttribute('data-field-id');
     if (!fieldId) {
         console.error("Main-Admin: Add Option button missing field ID.");
         return;
     }
     console.log(`Main-Admin: Add Option clicked for field ${fieldId}`);

     const optionsContainer = document.getElementById(`options-container-${fieldId}`);
     if (!optionsContainer) {
         console.error(`Main-Admin: Options container not found for field ${fieldId}`);
         return;
     }

     const nextOptionIndex = optionsContainer.children.length;

     // Create option with default label but empty selector
     const defaultOptionData = {
         label: `Option ${nextOptionIndex + 1}`,
         selector: '' // Empty selector by default
     };
     console.log(`Main-Admin: Creating new option with default data:`, defaultOptionData);

     const newOptionElement = createAdminOptionElement(fieldId, nextOptionIndex, defaultOptionData);
     optionsContainer.appendChild(newOptionElement);

     // No longer applying validation classes to new options
     const selectorInput = newOptionElement.querySelector('.option-selector');
     if (selectorInput) {
         // Remove any validation classes that might be present
         selectorInput.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

         // Remove any validation classes from the container
         newOptionElement.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
         console.log('Main-Admin: Removed validation classes from new option container');
     }

     initializeAdminOptionsAccordionAndSortable(optionsContainer);

     // Update the default option dropdown to include the new option
     updateDefaultOptionDropdown(fieldId);

     // Force another update after a short delay to ensure it's processed
     setTimeout(() => {
         updateDefaultOptionDropdown(fieldId);
     }, 200);

     newOptionElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
     $(optionsContainer).accordion('option', 'active', nextOptionIndex);
}

function handleRemoveOptionClick(button) {
    const optionContainer = button.closest('.option-item');
    if (!optionContainer) return;

    const optionLabel = optionContainer.querySelector('h4 > span')?.textContent || `this option`;
    const optionsWrapper = optionContainer.parentElement;

    // Get the field ID before removing the option
    const fieldId = optionContainer.getAttribute('data-field-id');
    console.log(`Main-Admin: Removing option with fieldId=${fieldId}`);

    if (confirm(`Are you sure you want to delete the option "${optionLabel}"? This cannot be undone.`)) {
        console.log("Main-Admin: Removing option element.");
        optionContainer.remove();
        if (optionsWrapper) {
            initializeAdminOptionsAccordionAndSortable(optionsWrapper);

            // Update the default option dropdown after removing the option
            if (fieldId) {
                updateDefaultOptionDropdown(fieldId);

                // Force another update after a short delay to ensure it's processed
                setTimeout(() => {
                    updateDefaultOptionDropdown(fieldId);
                }, 200);
            }
        }
    }
}

console.log("sidepanel-main-admin.js loaded");