// content-messaging.js - Message handling for communication with the extension

try {
    // Set up message listener for communication with the extension
    chrome.runtime.onMessage.addListener((message, _, sendResponse) => {
        let needsAsyncResponse = false;

        try {
            switch (message.action) {
                case 'ping':
                    // Simple ping to check if content script is loaded
                    sendResponse({ pong: true });
                    break;
                case 'enableSelectionMode':
                     needsAsyncResponse = true;
                    if (typeof window.enableSelectionMode === 'function') {
                        // Check if this is for selector generation only
                        const isSelectorGenerationOnly = message.payload?.isSelectorGenerationOnly === true;

                        // Pass the flag to the enableSelectionMode function
                        window.enableSelectionMode(isSelectorGenerationOnly);
                        sendResponse({ success: true });
                    } else {
                        console.error('enableSelectionMode function not available.');
                        sendResponse({ success: false, error: 'Selection mode function not found.' });
                    }
                    break;

                case 'cancelSelectionMode':
                    needsAsyncResponse = true;
                    if (typeof window.disableSelectionMode === 'function' && typeof window.isSelectionModeActive === 'function') {
                        if (window.isSelectionModeActive()) {
                            window.disableSelectionMode('canceled_from_sidepanel');
                            sendResponse({ success: true });
                        } else {
                            sendResponse({ success: true, info: 'Selection mode not active' });
                        }
                    } else {
                        console.error('disableSelectionMode function not available.');
                        sendResponse({ success: false, error: 'Selection mode functions not found.' });
                    }
                    break;

                case 'highlightElement':
                     needsAsyncResponse = true;
                     if (typeof window.highlightElement === 'function' && typeof window.getLastSelectedElement === 'function') {
                         const selectedElement = window.getLastSelectedElement();
                         if (selectedElement) {
                             window.highlightElement(selectedElement);
                             selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                             sendResponse({ success: true });
                         } else {
                             sendResponse({ success: false, error: 'No element selected previously.' });
                         }
                     } else {
                         sendResponse({ success: false, error: 'Highlighting function not found.' });
                     }
                    break;

                case 'getFullHTML':
                    // This action is now handled in content-actions.js
                    if (typeof window.handleElementAction === 'function') {
                        needsAsyncResponse = true;
                        window.handleElementAction(message, sendResponse);
                    } else {
                        console.error(`Handler for ${message.action} not available`);
                        sendResponse({ success: false, error: `Action handler not found for ${message.action}` });
                    }
                    break;

                case 'validateSelector':
                    needsAsyncResponse = true;
                    const validateSelector = message.payload?.selector;

                    // Use the validateSelector function from content-highlight.js
                    if (typeof window.validateSelector === 'function') {
                        const isValid = window.validateSelector(validateSelector);
                        sendResponse({ success: true, isValid: isValid });
                    } else {
                        console.warn('validateSelector function not available');
                        sendResponse({ success: true, isValid: true });
                    }
                    break;

                case 'highlightElementBySelector':
                    needsAsyncResponse = true;
                    const { selector: highlightSelector } = message.payload || {};

                    // Use the highlightElementBySelector function from content-highlight.js
                    if (typeof window.highlightElementBySelector === 'function') {
                        const highlighted = window.highlightElementBySelector(highlightSelector);
                        sendResponse({ success: true, highlighted: highlighted });
                    } else {
                        console.warn('highlightElementBySelector function not available');
                        sendResponse({ success: true, highlighted: true });
                    }
                    break;

                case 'removeValidationHighlight':
                    needsAsyncResponse = true;

                    // Use the removeValidationHighlight function from content-highlight.js
                    if (typeof window.removeValidationHighlight === 'function') {
                        window.removeValidationHighlight();
                        sendResponse({ success: true });
                    } else {
                        console.warn('removeValidationHighlight function not available');
                        sendResponse({ success: true });
                    }
                    break;

                case 'highlightMultipleElementsBySelectors':
                    needsAsyncResponse = true;
                    const { selectors } = message.payload || {};

                    // Use the highlightMultipleElementsBySelectors function from content-highlight.js
                    if (typeof window.highlightMultipleElementsBySelectors === 'function') {
                        const highlighted = window.highlightMultipleElementsBySelectors(selectors);
                        sendResponse({ success: true, highlighted: highlighted });
                    } else {
                        console.warn('highlightMultipleElementsBySelectors function not available');
                        sendResponse({ success: false, error: 'Highlight multiple function not available' });
                    }
                    break;

                case 'removeAllHighlights':
                    needsAsyncResponse = true;

                    // Use the removeAllHighlights function from content-highlight.js
                    if (typeof window.removeAllHighlights === 'function') {
                        window.removeAllHighlights();
                        sendResponse({ success: true });
                    } else {
                        console.warn('removeAllHighlights function not available');
                        sendResponse({ success: true });
                    }
                    break;

                case 'updateKnownSelectors':
                    needsAsyncResponse = true;
                    try {
                        const { selectors } = message.payload || {};
                        if (Array.isArray(selectors)) {
                            // Clear existing selectors and add new ones
                            window.knownSelectors.clear();
                            selectors.forEach(selector => {
                                if (selector && typeof selector === 'string' && selector.trim()) {
                                    window.knownSelectors.add(selector.trim());
                                }
                            });

                            // Dispatch event to clear the selector cache
                            document.dispatchEvent(new CustomEvent('selectorsCacheInvalidated'));
                            sendResponse({ success: true, count: window.knownSelectors.size });
                        } else {
                            console.warn('Invalid selectors array provided');
                            sendResponse({ success: false, error: 'Invalid selectors array' });
                        }
                    } catch (e) {
                        console.error('Error updating known selectors:', e);
                        sendResponse({ success: false, error: `Update selectors error: ${e.message}` });
                    }
                    break;

                // Element action handlers are in content-actions.js
                case 'setElementValue':
                case 'clickElement':
                case 'getSelectOptions':
                case 'selectOption':
                case 'setCheckboxState':
                case 'setRadioState':
                case 'uploadFileFromUrl':
                case 'simulateDragDropFile':
                case 'getFullHTML':
                case 'setIframeValue':
                    // These actions are handled in content-actions.js
                    if (typeof window.handleElementAction === 'function') {
                        needsAsyncResponse = true;
                        window.handleElementAction(message, sendResponse);
                    } else {
                        console.error(`Handler for ${message.action} not available`);
                        sendResponse({ success: false, error: `Action handler not found for ${message.action}` });
                    }
                    break;

                default:
                    console.warn("Unhandled action in content script:", message.action);
                    break;
            }
        } catch (error) {
            console.error('Content script message handler error:', error);
             if (needsAsyncResponse) {
                 try {
                     sendResponse({ success: false, error: `Content script error: ${error.message}` });
                 } catch (e) {
                     // Ignore errors in error handling
                 }
             }
        }
        return needsAsyncResponse;
    });

    // Set up window message listener for communication with element-utils.js
    window.addEventListener('message', (event) => {
        if (event.source !== window || !event.data || event.data.source !== 'form-analyzer-element-utils') {
            return;
        }

        switch(event.data.action) {
            case 'elementSelected':
                if (event.data.elementInfo) {
                    try {
                        chrome.runtime.sendMessage({
                            action: 'elementInfo',
                            data: event.data.elementInfo,
                            isSelectorGenerationOnly: event.data.isSelectorGenerationOnly === true
                        })
                            .catch(e => {
                                // Handle specific error for extension context invalidated
                                if (!e.message || !e.message.includes("Extension context invalidated")) {
                                    console.error("Error forwarding elementInfo:", e);
                                }
                            });
                    } catch (err) {
                        console.warn("Failed to send elementInfo message. Extension may have been reloaded:", err);
                    }
                } else {
                    console.warn("Internal message 'elementSelected' received without elementInfo.");
                }
                break;
            case 'selectionCompleted':
                 try {
                     chrome.runtime.sendMessage({ action: 'selectionComplete', reason: event.data.reason || 'completed' })
                         .catch(e => {
                             // Handle specific error for extension context invalidated
                             if (!e.message || !e.message.includes("Extension context invalidated")) {
                                 console.error("Error forwarding selectionComplete:", e);
                             }
                         });
                 } catch (err) {
                     console.warn("Failed to send selectionComplete message. Extension may have been reloaded:", err);
                 }
                break;
            default:
                console.warn("Unhandled internal message action from element-utils:", event.data.action);
                break;
        }
    });
} catch (e) {
    console.error("Form Analyzer: Failed to setup content script messaging:", e);
}
