// api-service.js - Shared API service for both popup and sidepanel

/**
 * Saves site configuration JSON to the backend API.
 * @param {string|null} siteId - The ID of the site to save configuration for (UUID format).
 * @param {object|Array} jsonData - The JSON data (form configuration) to save.
 * @returns {Promise<object>} - API response object { status: 'success'|'error', message: string, site_id?: string }
 */
async function saveSiteJSON(siteId, jsonData) {
  try {
    // The backend expects the JSON data as a string
    const jsonDataString = typeof jsonData === 'string' ? jsonData : JSON.stringify(jsonData);

    const response = await fetch('https://submitsaas.com/api/SaveSiteJSON', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        site_id: siteId,
        json_data: jsonDataString // Send as string
      })
    });

    const responseData = await response.json(); // Always try to parse JSON response

    if (!response.ok) {
      console.error('API Error (SaveSiteJSON):', response.status, responseData);
      return {
        status: 'error',
        message: `Server error ${response.status}: ${responseData.message || response.statusText}`
      };
    }

    console.log('SaveSiteJSON successful:', responseData);
    return { ...responseData, status: 'success' }; // Add success status

  } catch (error) {
    console.error('Error in saveSiteJSON:', error);
    return { status: 'error', message: `Network or fetch error: ${error.message}` };
  }
}

/**
 * Retrieves site configuration JSON from the backend API.
 * @param {string} siteUrl - The URL of the site to retrieve config for.
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, json_data: object|Array|null, site_id?: string }
 */
async function getSiteJSON(siteUrl) {
  try {
    const response = await fetch('https://submitsaas.com/api/GetSiteJSON', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: siteUrl })
    });

    const responseData = await response.json();

    if (!response.ok) {
        // Handle cases like 404 Not Found gracefully
        if (response.status === 404) {
             console.log(`No site JSON found for URL: ${siteUrl}`);
             return { status: 'not-found', message: 'No configuration found for this URL.', json_data: null, site_id: null, site_url: null };
        }
        console.error('API Error (GetSiteJSON):', response.status, responseData);
        return {
            status: 'error',
            message: `Server error ${response.status}: ${responseData.message || response.statusText}`,
            json_data: null,
             site_id: null,
             site_url: null
        };
    }

    console.log('GetSiteJSON successful:', responseData);
    // Attempt to parse json_data if it's a string, handle errors
    let parsedJsonData = null;
    if (responseData.json_data && typeof responseData.json_data === 'string') {
        try {
            parsedJsonData = JSON.parse(responseData.json_data);
        } catch (parseError) {
             console.error("Error parsing json_data string from API:", parseError);
             // Return success status but indicate data parsing issue
             return { status: 'success', message: 'Configuration found but failed to parse.', json_data: null, site_id: responseData.site_id, site_url: responseData.site_url };
        }
    } else {
         parsedJsonData = responseData.json_data; // Assume it's already an object/array or null
    }


    return { status: 'success', json_data: parsedJsonData, site_id: responseData.site_id, site_url: responseData.site_url };

  } catch (error) {
    console.error('Error in getSiteJSON:', error);
    return { status: 'error', message: `Network or fetch error: ${error.message}`, json_data: null, site_id: null, site_url: null };
  }
}

/**
 * Gets the URL of the currently active tab in the current window.
 * @returns {Promise<string|null>} The URL or null if unable to determine.
 */
async function getCurrentTabUrl() {
  try {
      if (!chrome?.tabs) {
           // Fallback for non-extension contexts (testing?)
           return window.location.href;
      }
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab?.url || null;
  } catch (error) {
      console.error("Error getting current tab URL:", error);
      return null;
  }
}

/**
 * Fetches the list of reports from the API.
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, reports: Array }
 */
async function getReports() {
  try {
    const response = await fetch('https://submitsaas.com/api/GetReports', {
      method: 'POST', // Assuming POST, adjust if GET
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}) // Empty body if needed
    });
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(`Server error ${response.status}: ${responseData.message || response.statusText}`);
    }
    console.log('GetReports successful');
    return { status: 'success', reports: responseData.reports || [] };
  } catch (error) {
    console.error('Error getting reports:', error);
    return { status: 'error', message: `Failed to get reports: ${error.message}`, reports: [] };
  }
}

/**
 * Saves the selected report ID to local storage.
 * @param {string|number} reportId - The ID of the report to save.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
async function saveSelectedReport(reportId) {
  return new Promise((resolve) => {
      if (!chrome?.storage?.local) {
           console.warn("Chrome storage not available, using localStorage (less reliable).");
           try { localStorage.setItem('selectedReportId', String(reportId)); resolve(true); } catch (e) { resolve(false); }
           return;
      }
    chrome.storage.local.set({ selectedReportId: reportId }, () => {
      resolve(!chrome.runtime.lastError);
      if (chrome.runtime.lastError) console.error('Error saving selected report ID:', chrome.runtime.lastError);
    });
  });
}

/**
 * Retrieves the selected report ID from local storage.
 * @returns {Promise<string|number|null>} - The report ID or null.
 */
async function getSelectedReport() {
  return new Promise((resolve) => {
       if (!chrome?.storage?.local) {
           console.warn("Chrome storage not available, using localStorage (less reliable).");
           resolve(localStorage.getItem('selectedReportId'));
           return;
       }
    chrome.storage.local.get('selectedReportId', (result) => {
      resolve(chrome.runtime.lastError ? null : result.selectedReportId || null);
       if (chrome.runtime.lastError) console.error('Error getting selected report ID:', chrome.runtime.lastError);
    });
  });
}

/**
 * Fetches form field key-value pairs for a specific report.
 * @param {string|number} reportId - The ID of the report.
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, fields: object }
 */
async function getFormFields(reportId) {
  try {
    const response = await fetch('https://submitsaas.com/api/GetFormFields', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ report_id: reportId })
    });
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(`Server error ${response.status}: ${responseData.message || response.statusText}`);
    }
    console.log('GetFormFields successful');
    return { status: 'success', fields: responseData.fields || {} };
  } catch (error) {
    console.error('Error getting form fields:', error);
    return { status: 'error', message: `Failed to get form fields: ${error.message}`, fields: {} };
  }
}

/**
 * Fetches specific site data linked to a report.
 * @param {string} reportId - The report ID (UUID format).
 * @param {string} siteId - The site ID (UUID format).
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, report_site: object|null }
 */
async function getReportSite(reportId, siteId) {
  try {
    const response = await fetch('https://submitsaas.com/api/GetReportSite', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ report_id: reportId, site_id: siteId })
    });
    const responseData = await response.json();
    if (!response.ok) {
         // Handle 404 gracefully
        if (response.status === 404) {
             console.log(`No report site data found for report ${reportId}, site ${siteId}`);
             return { status: 'not-found', message: 'No details found for this report/site combination.', report_site: null };
        }
      throw new Error(`Server error ${response.status}: ${responseData.message || response.statusText}`);
    }
    console.log('GetReportSite successful');
    return { status: 'success', report_site: responseData.report_site || null };
  } catch (error) {
    console.error('Error getting report site:', error);
    return { status: 'error', message: `Failed to get report site details: ${error.message}`, report_site: null };
  }
}

/**
 * Saves specific site data linked to a report.
 * @param {string} reportId - The report ID (UUID format).
 * @param {string} siteId - The site ID (UUID format).
 * @param {object} reportSiteData - The data to save (e.g., { checked: true, form_screenshot_url: '...' }).
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, report_site?: object }
 */
async function saveReportSite(reportId, siteId, reportSiteData) {
  try {
    const requestData = {
      report_id: reportId,
      site_id: siteId,
      ...reportSiteData
    };
    console.log('Saving report site with data:', requestData);
    const response = await fetch('https://submitsaas.com/api/SaveReportSite', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    });
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(`Server error ${response.status}: ${responseData.message || response.statusText}`);
    }
    console.log('SaveReportSite successful');
    return { status: 'success', report_site: responseData.report_site };
  } catch (error) {
    console.error('Error saving report site:', error);
    return { status: 'error', message: `Failed to save report site details: ${error.message}` };
  }
}

/**
 * Uploads an image (provided as base64 data URL) to the server.
 * @param {string} screenshotDataUrl - The screenshot data URL (e.g., "data:image/png;base64,...").
 * @returns {Promise<object>} - API response { status: 'success'|'error', message?: string, image_url: string|null }
 */
async function uploadImage(screenshotDataUrl) {
  try {
    console.log('Uploading image...');
    // Ensure it's a data URL and extract base64 part
    if (!screenshotDataUrl || !screenshotDataUrl.startsWith('data:image')) {
        throw new Error("Invalid screenshot data URL provided.");
    }
    const base64Data = screenshotDataUrl.split(',')[1];
    if (!base64Data) throw new Error("Could not extract base64 data from URL.");

    const response = await fetch('https://submitsaas.com/api/UploadBase64Image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ base64: base64Data })
    });

    const responseData = await response.json();
    if (!response.ok) {
      console.error('API Error (UploadImage):', response.status, responseData);
      throw new Error(`Server error ${response.status}: ${responseData.message || response.statusText}`);
    }

    console.log('Upload image successful:', responseData);
    if (!responseData.image_url) {
        console.warn("Image uploaded but no URL returned from API.");
         return { status: 'success', message: 'Upload OK, but no URL returned.', image_url: null };
    }

    return { status: 'success', image_url: responseData.image_url };

  } catch (error) {
    console.error('Error uploading image:', error);
    return { status: 'error', message: `Failed to upload image: ${error.message}`, image_url: null };
  }
}


console.log("Shared api-service.js loaded.");
