// popup.js

// DOM elements
const reportSelect = document.getElementById('reportSelect');
const aiModelSelect = document.getElementById('aiModelSelect');
const saveButton = document.getElementById('saveButton');
const messageElement = document.getElementById('message');

// Global variables
let reports = [];
let selectedReportId = null;
let selectedAiModel = 'openai'; // Default AI model

async function initPopup() {
  setLoading(true);
  try {
    await loadReports(); // Load available reports first
    await loadSelectedReport(); // Then load the currently selected one
    await loadAiModelPreference(); // Load AI model preference
    setupEventListeners();
  } catch (error) {
    console.error('Error initializing popup:', error);
    showMessage('Error loading reports. Please try again.', true);
  } finally {
    setLoading(false);
  }
}

async function loadAiModelPreference() {
  try {
    const result = await new Promise(resolve => {
      chrome.storage.local.get(['aiModel'], result => {
        resolve(chrome.runtime.lastError ? null : result);
      });
    });

    if (result?.aiModel) {
      selectedAiModel = result.aiModel;
      aiModelSelect.value = selectedAiModel;
      console.log('Loaded AI model preference:', selectedAiModel);
    } else {
      // Default to OpenAI if not set
      selectedAiModel = 'openai';
      aiModelSelect.value = selectedAiModel;
    }
  } catch (error) {
    console.error('Error loading AI model preference:', error);
    // Default to OpenAI on error
    selectedAiModel = 'openai';
    aiModelSelect.value = selectedAiModel;
  }
}

async function loadReports() {
  try {
    const result = await getReports(); // From api-service.js
    if (result.status === 'success' && Array.isArray(result.reports)) {
      reports = result.reports.filter(report => report.report_completed === false); // Filter incomplete
      populateReportSelect(reports);
      if (reports.length === 0) {
        showMessage('No incomplete reports available.', false);
        saveButton.disabled = true;
      } else {
         // Message set during loadSelectedReport or change handler
      }
    } else {
      throw new Error(result.message || 'Failed to fetch reports.');
    }
  } catch (error) {
    console.error('Error in loadReports:', error);
    showMessage(`Error loading reports: ${error.message}`, true);
    saveButton.disabled = true;
    reports = []; // Ensure reports is empty on error
  }
}

function populateReportSelect(reportsToPopulate) {
   while (reportSelect.options.length > 1) reportSelect.remove(1); // Clear existing
   reportsToPopulate.forEach(report => {
    const option = document.createElement('option');
    option.value = report.report_id;
    option.textContent = `${report.report_website_url || 'Unknown Client'} (ID: ${report.report_id})`;
    reportSelect.appendChild(option);
  });
}

async function loadSelectedReport() {
  try {
    const storageResult = await new Promise(resolve => {
        chrome.storage.local.get(['activeReport'], result => {
            resolve(chrome.runtime.lastError ? null : result);
        });
    });
    const activeReport = storageResult?.activeReport;

    if (activeReport?.report_id && reports.some(r => r.report_id == activeReport.report_id)) {
        selectedReportId = activeReport.report_id;
        reportSelect.value = selectedReportId;
        showMessage(`Current: ${activeReport.report_website_url || 'N/A'} (ID: ${selectedReportId})`, false);
    } else {
        // No valid active report in storage, or it's not in the current list
        selectedReportId = null;
        reportSelect.value = ""; // Reset dropdown
        if (reports.length > 0) {
             showMessage('Select an incomplete report.', false);
        } else {
             // Message already set by loadReports if none are available
        }
         // Clear potentially outdated active report from storage
         if (activeReport) {
             await new Promise(resolve => chrome.storage.local.remove('activeReport', resolve));
         }
    }
  } catch (error) {
    console.error('Error loading selected report:', error);
    showMessage('Error loading selection.', true);
    selectedReportId = null;
    reportSelect.value = "";
  } finally {
      saveButton.disabled = !reportSelect.value; // Ensure button state is correct
  }
}

function setupEventListeners() {
  saveButton.addEventListener('click', async () => {
    const reportId = reportSelect.value;
    if (!reportId) {
      showMessage('Please select a report.', true); return;
    }
    setLoading(true);
    try {
      const selectedReport = reports.find(r => r.report_id == reportId);
      if (!selectedReport) throw new Error('Selected report not found in list.');

      // 1. Save full report object to storage
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ activeReport: selectedReport }, () => {
          chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve();
        });
      });
      console.log('Active report saved to storage:', selectedReport);

      // 2. Save AI model preference to storage
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ aiModel: selectedAiModel }, () => {
          chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve();
        });
      });
      console.log('AI model preference saved to storage:', selectedAiModel);

      // 3. (Optional legacy) Save just ID if needed by other parts
      await saveSelectedReport(reportId); // from api-service.js

      selectedReportId = reportId; // Update local state
      showMessage(`Report "${selectedReport.report_website_url}" selected.`, false);

      // 4. Update report data and open side panel directly
      console.log('Updating report data and opening side panel directly');
      try {
          // Send update to notify other components
          await chrome.runtime.sendMessage({
            action: 'reportUpdated',
            report: selectedReport,
            aiModel: selectedAiModel
          });

          // Open side panel directly (this must be in direct response to user gesture)
          const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
          const activeTab = tabs?.[0];

          if (activeTab?.id) {
              // This will work because it's directly in the click handler chain
              await chrome.sidePanel.open({ tabId: activeTab.id });
              console.log(`Side panel opened for tab ${activeTab.id}`);
              // Close the popup after successful selection
              window.close();
          } else {
              throw new Error('No active tab found');
          }
      } catch (e) {
          console.warn("Error opening side panel:", e);
          showMessage(`Report selected, but couldn't open analyzer: ${e.message}`, true);
      }

    } catch (error) {
      console.error('Error saving selection:', error);
      showMessage(`Error saving: ${error.message}`, true);
    } finally {
      setLoading(false);
    }
  });

  reportSelect.addEventListener('change', () => {
    const reportId = reportSelect.value;
    saveButton.disabled = !reportId;
    if (reportId) {
        const selectedReport = reports.find(r => r.report_id == reportId);
        showMessage(`Select "${selectedReport?.report_website_url || 'N/A'}"?`, false);
    } else {
        showMessage('Select an incomplete report.', false);
    }
  });

  aiModelSelect.addEventListener('change', () => {
    selectedAiModel = aiModelSelect.value;
    console.log('AI model changed to:', selectedAiModel);

    // Save the AI model preference immediately
    chrome.storage.local.set({ aiModel: selectedAiModel }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error saving AI model preference:', chrome.runtime.lastError);
      } else {
        console.log('AI model preference saved to storage:', selectedAiModel);
      }
    });

    // Show a message to confirm the change
    showMessage(`AI model set to ${selectedAiModel === 'openai' ? 'OpenAI' : 'Google Gemini'}`, false);
  });
}

function showMessage(text, isError = false) {
  messageElement.textContent = text;
  messageElement.className = isError ? 'message error' : 'message'; // Use classes for styling
  messageElement.style.display = 'block';
}

function setLoading(isLoading) {
  document.body.classList.toggle('loading', isLoading);
  saveButton.disabled = isLoading || !reportSelect.value;
  reportSelect.disabled = isLoading;
}

// Add required CSS for loading class in popup.html if missing
/*
<style>
  ...
  body.loading { opacity: 0.7; pointer-events: none; }
  .message { margin-top: 10px; padding: 8px; border-radius: 3px; font-size: 12px; border-left: 4px solid; }
  .message.error { border-left-color: #dc3545; background-color: #f8d7da; color: #721c24;}
  .message:not(.error) { border-left-color: #28a745; background-color: #d4edda; color: #155724;}
</style>
*/

document.addEventListener('DOMContentLoaded', initPopup);