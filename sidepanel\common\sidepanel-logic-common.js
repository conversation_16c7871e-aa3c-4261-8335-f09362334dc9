

// Global state object
let state = {
    currentTabId: null,
    currentSiteId: null,
    currentSiteUrl: null, // The site URL from the database
    currentUrl: null, // The current tab URL
    activeReport: null,
    siteFormData: [],
    reportFieldValues: {},
    isGeneratingSelectorFor: null,
    currentSelectorPageId: null // Added to track which page a selector is being generated for
};

async function loadActiveReportDataAndUpdateUI() {
    try {
        const result = await new Promise(resolve => chrome.storage.local.get(['activeReport'], resolve));
        const storedReport = result?.activeReport || null;

        if (storedReport?.report_id !== state.activeReport?.report_id) {
            state.activeReport = storedReport;
            if (state.activeReport?.report_id) {
                await loadReportFieldValuesAndUpdateUI(state.activeReport.report_id);
            } else {
                state.reportFieldValues = {};
                updateSubmitFormFields(state.siteFormData);
            }
        }
        await fetchReportSiteDataAndUpdateUI();
        updateUnifiedStatus();
    } catch (error) {
        console.error("Logic-Common: Error loading active report data:", error);
        state.activeReport = null;
        state.reportFieldValues = {};
        clearReportSiteFields();
        updateSubmitFormFields(state.siteFormData);
        updateUnifiedStatus();
    }
}

function clearSiteDataState() {
    state.currentSiteId = null;
    state.currentSiteUrl = null;
    state.siteFormData = [];
}

function handleStorageChange(changes, area) {
    if (area === 'local' && changes.activeReport) {
        loadActiveReportDataAndUpdateUI();
    }
}

function handleRuntimeMessage(message, sender, sendResponse) {
    // Generate a unique ID for logging purposes if needed
    // const messageId = Math.random().toString(36).substring(2, 9);

    const isFromBackground = !sender.tab && sender.url?.includes('background');
    const isFromCurrentTabContentScript = message.originatingTabId === state.currentTabId;

    // For element selection messages, check if this sidepanel is the intended target
    if ((message.action === 'elementInfo' || message.action === 'selectionComplete') &&
        message.targetTabId && message.targetTabId !== state.currentTabId) {
        return false;
    }

    // For other messages, use the original check
    if (!isFromBackground && !isFromCurrentTabContentScript) {
        return false;
    }
    let needsAsyncResponse = false;

    try {
        switch (message.action) {
            case 'elementInfo':
                // Double-check that this message is intended for this sidepanel
                if (message.targetTabId && message.targetTabId !== state.currentTabId) {
                    break;
                }

                const currentIsGenerating = state.isGeneratingSelectorFor;
                const isSelectorGenerationOnly = message.isSelectorGenerationOnly === true;

                // Check both the state variable and the message flag
                if (currentIsGenerating || isSelectorGenerationOnly) {
                    // Populate the selector input with the selected element's selector
                    populateSelectorFromSelection(message.data);
                    resetSelectButtons();

                    // Important: Return early to prevent processSelectedElement from being called
                    // This ensures the AI API is not called when we're just generating a selector
                    return false;
                } else {
                    processSelectedElement(message.data);
                }
                break;

            case 'selectionComplete':
                // Double-check that this message is intended for this sidepanel
                if (message.targetTabId && message.targetTabId !== state.currentTabId) {
                    break;
                }

                if (state.isGeneratingSelectorFor) {
                    state.isGeneratingSelectorFor = null;
                }

                // Always reset the selector generation mode flag on selection complete
                window.isSelectorGenerationMode = false;

                resetSelectButtons();

                 if (message.reason === 'canceled') {
                    alert("Element selection canceled.");
                 }
                break;

            case 'reportUpdated':
                loadActiveReportDataAndUpdateUI();
                break;

            case 'urlChanged':
                 if (message.tabId && message.tabId === state.currentTabId) {
                     state.currentUrl = message.newUrl;
                     loadSiteJSONDataAndUpdateUI(message.newUrl);
                 }
                 break;

            case 'activeTabClosed':
                 state.currentTabId = null;
                 state.currentUrl = null;
                 clearSidePanelContent();
                 break;

            default:
                 break;

        }
        if (message.requiresResponse && !needsAsyncResponse) {
           try { sendResponse({ success: true }); } catch(e) { }
        }
    } catch (error) {
        console.error(`Logic-Common: Error handling message ${message.action}:`, error);
        if (message.requiresResponse) {
            try { sendResponse({ success: false, error: error.message }); } catch (e) { }
        }
         state.isGeneratingSelectorFor = null;
         resetSelectButtons();
    }
    return needsAsyncResponse;
}


function populateSelectorFromSelection(elementInfo) {
    const targetInputId = state.isGeneratingSelectorFor;
    const pageId = state.currentSelectorPageId;

    const currentlyProcessingTarget = targetInputId;
    state.isGeneratingSelectorFor = null;
    state.currentSelectorPageId = null;

    // Reset the selector generation mode flag
    window.isSelectorGenerationMode = false;

    if (!currentlyProcessingTarget) {
        return false;
    }

    // First try to find the input in the specific page if we have a page ID
    let targetInput = null;

    if (pageId) {
        // Find the page container first
        const pageContainer = document.querySelector(`.page-accordion[data-page-id="${pageId}"]`);
        if (pageContainer) {
            // Then find the input within that page container
            targetInput = pageContainer.querySelector(`#${currentlyProcessingTarget}`);
        }
    }

    // Fall back to document.getElementById if we couldn't find it in the specific page
    if (!targetInput) {
        targetInput = document.getElementById(currentlyProcessingTarget);
    }

    const selectorValue = elementInfo?.absolutePath;

    if (!targetInput) {
        alert(`Error: Could not find input field ${currentlyProcessingTarget} to update.`);
        return false;
    }

    if (!selectorValue) {
         alert("Failed to get selector path from selection.");
         targetInput.value = '';
         return false;
    }

    // Set the value and trigger all relevant events
    targetInput.value = selectorValue;

    // Trigger input event first (mimics user typing)
    targetInput.dispatchEvent(new Event('input', { bubbles: true }));

    // Then trigger change event (mimics input losing focus after change)
    targetInput.dispatchEvent(new Event('change', { bubbles: true }));

    // Also trigger blur event to ensure all handlers run
    targetInput.dispatchEvent(new Event('blur', { bubbles: true }));

    // For good measure, trigger a keyup event as well
    targetInput.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));

    return true;
}


// These functions have been moved to separate files:
// - minimizeHtmlRegex -> sidepanel-html-utils.js
// - callOpenAIForFormJSON -> sidepanel-ai-service.js
// - unescapeString -> sidepanel-html-utils.js
// - findBestMatchingOption -> sidepanel-ai-service.js

